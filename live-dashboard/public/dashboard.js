// Initialize Socket.IO connection
const socket = io();

// DOM elements
const statusIndicator = document.getElementById('status-indicator');
const startScanBtn = document.getElementById('start-scan-btn');
const parametersBtn = document.getElementById('parameters-btn');
const logsBtn = document.getElementById('logs-btn');
const citiesProcessed = document.getElementById('cities-processed');
const propertiesFound = document.getElementById('properties-found');
const highPriority = document.getElementById('high-priority');
const apiCalls = document.getElementById('api-calls');
const cycleTime = document.getElementById('cycle-time');
const cityProgress = document.getElementById('city-progress');
const recentProperties = document.getElementById('recent-properties');
const activityLog = document.getElementById('activity-log');

// Modal elements
const parametersModal = document.getElementById('parameters-modal');
const logsModal = document.getElementById('logs-modal');
const closeParameters = document.getElementById('close-parameters');
const closeLogs = document.getElementById('close-logs');
const saveParameters = document.getElementById('save-parameters');
const resetParameters = document.getElementById('reset-parameters');
const clearLogs = document.getElementById('clear-logs');
const detailedLogs = document.getElementById('detailed-logs');
const apiExample = document.getElementById('api-example');

// State
let monitoringState = {};
let cycleStartTime = null;
let cycleTimer = null;
let detailedLogEntries = [];
let currentParameters = {
    bedsMin: 2,
    bedsMax: 5,
    valueMin: 30000,
    valueMax: 400000,
    propertyType: 'SFR',
    resultsLimit: 25,
    scanInterval: 5,
    concurrentCities: 3,
    apiTimeout: 15,
    highThreshold: 80,
    mediumThreshold: 60,
    rent2br: 800,
    rent3br: 1000
};

// Socket event handlers
socket.on('connect', () => {
    updateStatusIndicator('connected', 'Connected');
    addActivityLog('🟢 Connected to monitoring system', 'success');
});

socket.on('disconnect', () => {
    updateStatusIndicator('disconnected', 'Disconnected');
    addActivityLog('🔴 Disconnected from monitoring system', 'error');
});

socket.on('monitoring-state', (state) => {
    monitoringState = state;
    updateDashboard();
    initializeCityProgress();
});

socket.on('cycle-started', (data) => {
    cycleStartTime = new Date(data.startTime);
    startCycleTimer();
    updateStartButton(true);
    addActivityLog(`🚀 Monitoring cycle started - scanning ${data.totalCities} cities`, 'info');
    updateStatusIndicator('scanning', 'Scanning...');
});

socket.on('city-update', (data) => {
    updateCityStatus(data.city, data.status);
    if (data.status === 'processing') {
        addActivityLog(`🔍 Scanning ${data.city}...`, 'info');
    }
});

socket.on('city-completed', (data) => {
    updateCityStatus(data.city, 'completed', data);
    addActivityLog(
        `✅ ${data.city} completed: ${data.propertiesFound} properties found (${data.highPriorityFound} high priority)`, 
        'success'
    );
});

socket.on('city-error', (data) => {
    updateCityStatus(data.city, 'error');
    addActivityLog(`❌ Error scanning ${data.city}: ${data.error}`, 'error');
});

socket.on('property-found', (property) => {
    addRecentProperty(property);
    addActivityLog(
        `🏠 NEW ${property.priority} PRIORITY: ${property.address}, ${property.city} - Score: ${property.investmentScore}`, 
        property.priority === 'HIGH' ? 'success' : 'info'
    );
});

socket.on('cycle-completed', (data) => {
    stopCycleTimer();
    updateStartButton(false);
    updateStatusIndicator('completed', 'Cycle Complete');
    addActivityLog(
        `🎉 Cycle completed in ${data.duration.toFixed(1)}s: ${data.propertiesFound} properties, ${data.highPriorityFound} high priority`, 
        'success'
    );
    
    // Auto-start next cycle after 30 seconds
    setTimeout(() => {
        if (!monitoringState.isRunning) {
            addActivityLog('⏰ Starting next cycle...', 'info');
            startScan();
        }
    }, 30000);
});

socket.on('cycle-error', (data) => {
    stopCycleTimer();
    updateStartButton(false);
    updateStatusIndicator('error', 'Error');
    addActivityLog(`💥 Cycle error: ${data.error}`, 'error');
});

socket.on('detailed-log', (logData) => {
    addDetailedLog(logData.type, logData.message, logData.data);
});

socket.on('current-parameters', (parameters) => {
    currentParameters = { ...currentParameters, ...parameters };
});

socket.on('parameters-updated', (parameters) => {
    currentParameters = { ...currentParameters, ...parameters };
    addActivityLog('⚙️ Parameters synchronized from server', 'info');
});

// UI Update Functions
function updateStatusIndicator(status, text) {
    const indicator = statusIndicator.querySelector('div');
    const span = statusIndicator.querySelector('span');
    
    indicator.className = 'w-3 h-3 rounded-full';
    
    switch (status) {
        case 'connected':
            indicator.classList.add('bg-green-400');
            break;
        case 'scanning':
            indicator.classList.add('bg-blue-400', 'pulse-animation');
            break;
        case 'completed':
            indicator.classList.add('bg-green-400');
            break;
        case 'error':
            indicator.classList.add('bg-red-400');
            break;
        default:
            indicator.classList.add('bg-gray-400');
    }
    
    span.textContent = text;
}

function updateDashboard() {
    citiesProcessed.textContent = monitoringState.citiesProcessed || 0;
    propertiesFound.textContent = monitoringState.propertiesFound || 0;
    highPriority.textContent = monitoringState.highPriorityFound || 0;
    apiCalls.textContent = monitoringState.apiCallsMade || 0;
}

function updateStartButton(isRunning) {
    if (isRunning) {
        startScanBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Scanning...';
        startScanBtn.disabled = true;
        startScanBtn.classList.add('opacity-50', 'cursor-not-allowed');
    } else {
        startScanBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Start Scan';
        startScanBtn.disabled = false;
        startScanBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    }
}

function initializeCityProgress() {
    const cities = [
        { city: "DETROIT", state: "MI", priority: "VERY_HIGH" },
        { city: "CLEVELAND", state: "OH", priority: "VERY_HIGH" },
        { city: "PITTSBURGH", state: "PA", priority: "VERY_HIGH" },
        { city: "BUFFALO", state: "NY", priority: "VERY_HIGH" },
        { city: "MILWAUKEE", state: "WI", priority: "VERY_HIGH" },
        { city: "AKRON", state: "OH", priority: "HIGH" },
        { city: "TOLEDO", state: "OH", priority: "HIGH" },
        { city: "YOUNGSTOWN", state: "OH", priority: "HIGH" },
        { city: "DAYTON", state: "OH", priority: "HIGH" },
        { city: "FLINT", state: "MI", priority: "HIGH" },
        { city: "BIRMINGHAM", state: "AL", priority: "HIGH" },
        { city: "MEMPHIS", state: "TN", priority: "HIGH" },
        { city: "JACKSON", state: "MS", priority: "HIGH" },
        { city: "NEW BERN", state: "NC", priority: "HIGH" },
        { city: "WILMINGTON", state: "NC", priority: "HIGH" },
        { city: "FORT WAYNE", state: "IN", priority: "MEDIUM" },
        { city: "EVANSVILLE", state: "IN", priority: "MEDIUM" },
        { city: "SPRINGFIELD", state: "IL", priority: "MEDIUM" },
        { city: "ROCKFORD", state: "IL", priority: "MEDIUM" },
        { city: "PEORIA", state: "IL", priority: "MEDIUM" }
    ];
    
    cityProgress.innerHTML = '';
    
    cities.forEach(city => {
        const cityKey = `${city.city}, ${city.state}`;
        const cityElement = createCityElement(cityKey, city.priority);
        cityProgress.appendChild(cityElement);
    });
}

function createCityElement(cityKey, priority) {
    const div = document.createElement('div');
    div.id = `city-${cityKey.replace(/[^a-zA-Z0-9]/g, '-')}`;
    div.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
    
    const priorityColor = {
        'VERY_HIGH': 'text-red-600',
        'HIGH': 'text-orange-600',
        'MEDIUM': 'text-yellow-600'
    }[priority] || 'text-gray-600';
    
    div.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="w-3 h-3 bg-gray-300 rounded-full status-indicator"></div>
            <div>
                <div class="font-medium text-gray-900">${cityKey}</div>
                <div class="text-sm ${priorityColor}">${priority} Priority</div>
            </div>
        </div>
        <div class="text-right">
            <div class="text-sm font-medium text-gray-900 properties-count">--</div>
            <div class="text-xs text-gray-500 status-text">Pending</div>
        </div>
    `;
    
    return div;
}

function updateCityStatus(cityKey, status, data = {}) {
    const cityId = `city-${cityKey.replace(/[^a-zA-Z0-9]/g, '-')}`;
    const cityElement = document.getElementById(cityId);
    
    if (!cityElement) return;
    
    const statusIndicator = cityElement.querySelector('.status-indicator');
    const statusText = cityElement.querySelector('.status-text');
    const propertiesCount = cityElement.querySelector('.properties-count');
    
    // Reset classes
    statusIndicator.className = 'w-3 h-3 rounded-full status-indicator';
    cityElement.className = 'flex items-center justify-between p-3 rounded-lg';
    
    switch (status) {
        case 'processing':
            statusIndicator.classList.add('bg-blue-400', 'pulse-animation');
            statusText.textContent = 'Scanning...';
            cityElement.classList.add('city-processing', 'text-white');
            break;
        case 'completed':
            statusIndicator.classList.add('bg-green-400');
            statusText.textContent = 'Completed';
            propertiesCount.textContent = `${data.propertiesFound || 0} found`;
            cityElement.classList.add('bg-green-50');
            break;
        case 'error':
            statusIndicator.classList.add('bg-red-400');
            statusText.textContent = 'Error';
            cityElement.classList.add('bg-red-50');
            break;
        default:
            statusIndicator.classList.add('bg-gray-300');
            statusText.textContent = 'Pending';
            cityElement.classList.add('bg-gray-50');
    }
}

function addRecentProperty(property) {
    const propertyElement = document.createElement('div');
    propertyElement.className = `slide-in p-4 bg-gray-50 rounded-lg priority-${property.priority.toLowerCase()}`;
    
    const priorityColor = {
        'HIGH': 'text-red-600 bg-red-100',
        'MEDIUM': 'text-yellow-600 bg-yellow-100',
        'LOW': 'text-gray-600 bg-gray-100'
    }[property.priority] || 'text-gray-600 bg-gray-100';
    
    propertyElement.innerHTML = `
        <div class="flex justify-between items-start mb-2">
            <div class="text-sm font-medium text-gray-900">${property.address}</div>
            <span class="px-2 py-1 text-xs font-medium rounded-full ${priorityColor}">
                ${property.priority}
            </span>
        </div>
        <div class="text-xs text-gray-600 mb-2">${property.city}, ${property.state}</div>
        <div class="grid grid-cols-2 gap-2 text-xs">
            <div>
                <span class="text-gray-500">Value:</span>
                <span class="font-medium">$${property.estimatedValue.toLocaleString()}</span>
            </div>
            <div>
                <span class="text-gray-500">Score:</span>
                <span class="font-medium">${property.investmentScore}</span>
            </div>
            <div>
                <span class="text-gray-500">Rent:</span>
                <span class="font-medium">$${property.estimatedRent}</span>
            </div>
            <div>
                <span class="text-gray-500">Cash Flow:</span>
                <span class="font-medium ${property.cashFlow >= 0 ? 'text-green-600' : 'text-red-600'}">
                    $${property.cashFlow}
                </span>
            </div>
        </div>
    `;
    
    // Clear "waiting" message if present
    if (recentProperties.querySelector('.text-center')) {
        recentProperties.innerHTML = '';
    }
    
    recentProperties.insertBefore(propertyElement, recentProperties.firstChild);
    
    // Keep only last 5 properties
    while (recentProperties.children.length > 5) {
        recentProperties.removeChild(recentProperties.lastChild);
    }
}

function addActivityLog(message, type = 'info') {
    const logElement = document.createElement('div');
    logElement.className = 'flex items-center space-x-3 p-2 rounded text-sm';
    
    const typeColors = {
        'info': 'bg-blue-50 text-blue-800',
        'success': 'bg-green-50 text-green-800',
        'error': 'bg-red-50 text-red-800',
        'warning': 'bg-yellow-50 text-yellow-800'
    };
    
    logElement.classList.add(typeColors[type] || typeColors.info);
    
    const timestamp = new Date().toLocaleTimeString();
    logElement.innerHTML = `
        <span class="text-xs text-gray-500 w-20">${timestamp}</span>
        <span class="flex-1">${message}</span>
    `;
    
    activityLog.insertBefore(logElement, activityLog.firstChild);
    
    // Keep only last 50 log entries
    while (activityLog.children.length > 50) {
        activityLog.removeChild(activityLog.lastChild);
    }
}

function startCycleTimer() {
    cycleTimer = setInterval(() => {
        if (cycleStartTime) {
            const elapsed = (new Date() - cycleStartTime) / 1000;
            cycleTime.textContent = `${elapsed.toFixed(0)}s`;
        }
    }, 1000);
}

function stopCycleTimer() {
    if (cycleTimer) {
        clearInterval(cycleTimer);
        cycleTimer = null;
    }
}

function startScan() {
    socket.emit('start-scan');
}

// Modal Functions
function showParametersModal() {
    // Populate current values
    document.getElementById('api-url').value = 'https://api.realestateapi.com/v2/PropertySearch';
    document.getElementById('api-key').value = 'AYOKASYS...ab2914';
    document.getElementById('database-url').value = 'postgresql://***@***.neon.tech/***';

    document.getElementById('beds-min').value = currentParameters.bedsMin;
    document.getElementById('beds-max').value = currentParameters.bedsMax;
    document.getElementById('value-min').value = currentParameters.valueMin;
    document.getElementById('value-max').value = currentParameters.valueMax;
    document.getElementById('property-type').value = currentParameters.propertyType;
    document.getElementById('results-limit').value = currentParameters.resultsLimit;
    document.getElementById('scan-interval').value = currentParameters.scanInterval;
    document.getElementById('concurrent-cities').value = currentParameters.concurrentCities;
    document.getElementById('api-timeout').value = currentParameters.apiTimeout;
    document.getElementById('high-threshold').value = currentParameters.highThreshold;
    document.getElementById('medium-threshold').value = currentParameters.mediumThreshold;
    document.getElementById('rent-2br').value = currentParameters.rent2br;
    document.getElementById('rent-3br').value = currentParameters.rent3br;

    updateApiExample();
    parametersModal.classList.remove('hidden');
}

function hideParametersModal() {
    parametersModal.classList.add('hidden');
}

function showLogsModal() {
    updateDetailedLogs();
    logsModal.classList.remove('hidden');
}

function hideLogsModal() {
    logsModal.classList.add('hidden');
}

function updateApiExample() {
    const example = {
        url: 'https://api.realestateapi.com/v2/PropertySearch',
        method: 'POST',
        headers: {
            'X-API-Key': 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914',
            'Content-Type': 'application/json'
        },
        body: {
            city: 'DETROIT',
            state: 'MI',
            beds_min: currentParameters.bedsMin,
            beds_max: currentParameters.bedsMax,
            value_min: currentParameters.valueMin,
            value_max: currentParameters.valueMax,
            property_type: currentParameters.propertyType,
            limit: currentParameters.resultsLimit
        }
    };

    apiExample.textContent = JSON.stringify(example, null, 2);
}

function saveParametersChanges() {
    // Update parameters from form
    currentParameters.bedsMin = parseInt(document.getElementById('beds-min').value);
    currentParameters.bedsMax = parseInt(document.getElementById('beds-max').value);
    currentParameters.valueMin = parseInt(document.getElementById('value-min').value);
    currentParameters.valueMax = parseInt(document.getElementById('value-max').value);
    currentParameters.propertyType = document.getElementById('property-type').value;
    currentParameters.resultsLimit = parseInt(document.getElementById('results-limit').value);
    currentParameters.scanInterval = parseInt(document.getElementById('scan-interval').value);
    currentParameters.concurrentCities = parseInt(document.getElementById('concurrent-cities').value);
    currentParameters.apiTimeout = parseInt(document.getElementById('api-timeout').value);
    currentParameters.highThreshold = parseInt(document.getElementById('high-threshold').value);
    currentParameters.mediumThreshold = parseInt(document.getElementById('medium-threshold').value);
    currentParameters.rent2br = parseInt(document.getElementById('rent-2br').value);
    currentParameters.rent3br = parseInt(document.getElementById('rent-3br').value);

    // Send to server
    socket.emit('update-parameters', currentParameters);

    addActivityLog('⚙️ Parameters updated and saved', 'info');
    addDetailedLog('PARAMETERS', 'Parameters updated', currentParameters);
    hideParametersModal();
}

function resetParametersToDefaults() {
    currentParameters = {
        bedsMin: 2,
        bedsMax: 5,
        valueMin: 30000,
        valueMax: 400000,
        propertyType: 'SFR',
        resultsLimit: 25,
        scanInterval: 5,
        concurrentCities: 3,
        apiTimeout: 15,
        highThreshold: 80,
        mediumThreshold: 60,
        rent2br: 800,
        rent3br: 1000
    };

    showParametersModal(); // Refresh the modal with defaults
    addActivityLog('🔄 Parameters reset to defaults', 'info');
}

function addDetailedLog(type, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
        timestamp,
        type,
        message,
        data
    };

    detailedLogEntries.unshift(logEntry);

    // Keep only last 100 entries
    if (detailedLogEntries.length > 100) {
        detailedLogEntries = detailedLogEntries.slice(0, 100);
    }
}

function updateDetailedLogs() {
    detailedLogs.innerHTML = '';

    if (detailedLogEntries.length === 0) {
        detailedLogs.innerHTML = '<div class="text-gray-500 text-center py-8">No detailed logs available</div>';
        return;
    }

    detailedLogEntries.forEach(entry => {
        const logDiv = document.createElement('div');
        logDiv.className = 'p-3 border-l-4 border-gray-300 bg-gray-50 rounded';

        const typeColors = {
            'API_CALL': 'border-blue-400 bg-blue-50',
            'DATABASE': 'border-green-400 bg-green-50',
            'ERROR': 'border-red-400 bg-red-50',
            'PARAMETERS': 'border-purple-400 bg-purple-50',
            'SYSTEM': 'border-yellow-400 bg-yellow-50'
        };

        if (typeColors[entry.type]) {
            logDiv.className = `p-3 border-l-4 ${typeColors[entry.type]} rounded`;
        }

        logDiv.innerHTML = `
            <div class="flex justify-between items-start mb-2">
                <span class="font-medium text-sm">[${entry.type}] ${entry.message}</span>
                <span class="text-xs text-gray-500">${new Date(entry.timestamp).toLocaleTimeString()}</span>
            </div>
            ${entry.data ? `<pre class="text-xs text-gray-700 mt-2 overflow-x-auto">${JSON.stringify(entry.data, null, 2)}</pre>` : ''}
        `;

        detailedLogs.appendChild(logDiv);
    });
}

function clearDetailedLogs() {
    detailedLogEntries = [];
    updateDetailedLogs();
    addActivityLog('🗑️ Detailed logs cleared', 'info');
}

// Enhanced addActivityLog to also add to detailed logs
const originalAddActivityLog = addActivityLog;
function addActivityLog(message, type = 'info') {
    originalAddActivityLog(message, type);

    // Also add to detailed logs
    const logType = type.toUpperCase() === 'ERROR' ? 'ERROR' : 'SYSTEM';
    addDetailedLog(logType, message);
}

// Event listeners
startScanBtn.addEventListener('click', startScan);
parametersBtn.addEventListener('click', showParametersModal);
logsBtn.addEventListener('click', showLogsModal);
closeParameters.addEventListener('click', hideParametersModal);
closeLogs.addEventListener('click', hideLogsModal);
saveParameters.addEventListener('click', saveParametersChanges);
resetParameters.addEventListener('click', resetParametersToDefaults);
clearLogs.addEventListener('click', clearDetailedLogs);

// Close modals when clicking outside
parametersModal.addEventListener('click', (e) => {
    if (e.target === parametersModal) hideParametersModal();
});

logsModal.addEventListener('click', (e) => {
    if (e.target === logsModal) hideLogsModal();
});

// Update parameters form when values change
document.getElementById('beds-min').addEventListener('input', updateApiExample);
document.getElementById('beds-max').addEventListener('input', updateApiExample);
document.getElementById('value-min').addEventListener('input', updateApiExample);
document.getElementById('value-max').addEventListener('input', updateApiExample);
document.getElementById('property-type').addEventListener('change', updateApiExample);
document.getElementById('results-limit').addEventListener('input', updateApiExample);

// Initialize dashboard
addActivityLog('🚀 Dashboard initialized', 'info');
addDetailedLog('SYSTEM', 'Dashboard initialized', {
    timestamp: new Date().toISOString(),
    parameters: currentParameters,
    socketConnected: false
});

// Show current "0 found" status prominently
addActivityLog('📊 Current Status: 0 properties found - Ready to start scanning', 'info');
addDetailedLog('SYSTEM', 'Initial state', {
    propertiesFound: 0,
    highPriorityFound: 0,
    citiesProcessed: 0,
    apiCallsMade: 0
});
