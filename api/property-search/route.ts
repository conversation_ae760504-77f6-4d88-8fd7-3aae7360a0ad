import { NextRequest, NextResponse } from 'next/server';

const EXTERNAL_API_URL = 'https://api.realestateapi.com/v2/PropertySearch';

// Define types for search profiles
interface SearchProfile {
  name: string;
  description: string;
  tags: string[];
  searchParameters: Record<string, any>;
}

type SearchProfileKey = 
  | 'small-multi-family'
  | 'affordable-sfr'
  | 'absentee-landlord'
  | 'corporate-owned'
  | 'high-vacancy'
  | 'high-fmr-areas'
  | 'cash-flow-king'
  | 'brrrr-strategy'
  | 'distressed-properties';

// Section 8 search profiles
const SECTION8_SEARCH_PROFILES: Record<string, SearchProfile> = {
  'small-multi-family': {
    name: 'Small Multi-Units (2-4 Units)',
    description: 'Duplexes, triplexes and quadplexes ideal for smaller Section 8 investments',
    tags: ['section8', 'investment', 'beginner'],
    searchParameters: {
      mfh_2to4: true,
      units_min: 2,
      units_max: 4
    }
  },
  'affordable-sfr': {
    name: 'Affordable Single Family',
    description: 'Single family homes in price ranges appropriate for Section 8 vouchers',
    tags: ['section8', 'sfr'],
    searchParameters: {
      property_type: 'SFR',
      beds_min: 2,
      baths_min: 1
    }
  },
  'absentee-landlord': {
    name: 'Absentee Landlord Properties',
    description: 'Properties where owner doesn\'t live on site, typically indicating rental properties',
    tags: ['section8', 'investor-owned'],
    searchParameters: {
      absentee_owner: true
    }
  },
  'corporate-owned': {
    name: 'Corporate Owned Rentals',
    description: 'Investment properties owned by corporations often accepting Section 8',
    tags: ['section8', 'investor-owned'],
    searchParameters: {
      corporate_owned: true
    }
  },
  'high-vacancy': {
    name: 'Vacant Properties',
    description: 'Currently vacant properties that could be converted to Section 8 rentals',
    tags: ['section8', 'value-add'],
    searchParameters: {
      vacant: true
    }
  },
  'high-fmr-areas': {
    name: 'High FMR Areas',
    description: 'Areas with high Fair Market Rent values relative to property prices',
    tags: ['section8', 'market'],
    searchParameters: {
      mls_active: true,
      sort: { "value": "asc" }
    }
  }
};

// Investment-focused search profiles
const INVESTMENT_SEARCH_PROFILES: Record<string, SearchProfile> = {
  'cash-flow-king': {
    name: 'Cash Flow King',
    description: 'Properties with strong cash flow potential based on area rent-to-price ratios',
    tags: ['investment', 'cash-flow'],
    searchParameters: {
      value_max: 250000,
      mls_active: true,
      cash_buyer: true,
      sort: { "value": "asc" }
    }
  },
  'brrrr-strategy': {
    name: 'BRRRR Strategy Properties',
    description: 'Buy, Rehab, Rent, Refinance, Repeat - Properties ideal for the BRRRR strategy',
    tags: ['investment', 'value-add', 'brrrr'],
    searchParameters: {
      foreclosure: true,
      pre_foreclosure: true,
      vacant: true,
      year_built_min: 1950,
      absentee_owner: true
    }
  },
  'distressed-properties': {
    name: 'Distressed Properties',
    description: 'Properties that may be available at below-market prices due to various distress factors',
    tags: ['investment', 'value-add', 'distressed'],
    searchParameters: {
      foreclosure: true,
      tax_lien: true,
      high_equity: true,
      mls_days_on_market_min: 90
    }
  }
};

// Combine all search profiles
const ALL_SEARCH_PROFILES: Record<string, SearchProfile> = {
  ...SECTION8_SEARCH_PROFILES,
  ...INVESTMENT_SEARCH_PROFILES
};

// Define request body type
interface PropertySearchRequest {
  // Location parameters
  city?: string;
  state?: string;
  zip?: string;
  address?: string;
  county?: string;
  latitude?: number;
  longitude?: number;
  radius?: number;
  
  // Property type
  property_type?: string;
  propertyType?: string; // backward compatibility
  
  // Property details
  beds_min?: number;
  beds_max?: number;
  baths_min?: number;
  baths_max?: number;
  building_size_min?: number;
  building_size_max?: number;
  lot_size_min?: number;
  lot_size_max?: number;
  year_built_min?: number;
  year_built_max?: number;
  units_min?: number;
  units_max?: number;
  
  // Financial filters
  value_min?: number;
  value_max?: number;
  equity_min?: number;
  equity_max?: number;
  sale_price_min?: number;
  sale_price_max?: number;
  sale_date_min?: string;
  sale_date_max?: string;
  
  // MLS parameters
  mls_active?: boolean;
  mlsActive?: boolean;
  mls_pending?: boolean;
  mlsPending?: boolean;
  mls_sold?: boolean;
  mlsSold?: boolean;
  mls_days_on_market_min?: number;
  mls_days_on_market_max?: number;
  
  // Ownership filters
  absentee_owner?: boolean;
  corporate_owned?: boolean;
  trust_owned?: boolean;
  llc_owned?: boolean;
  owner_occupied?: boolean;
  ownership_length_min?: number;
  ownership_length_max?: number;
  
  // Property condition
  vacant?: boolean;
  distressed?: boolean;
  foreclosure?: boolean;
  pre_foreclosure?: boolean;
  auction?: boolean;
  tax_lien?: boolean;
  code_violation?: boolean;
  flood_zone?: string;
  
  // Investment filters
  high_equity?: boolean;
  negative_equity?: boolean;
  free_clear?: boolean;
  cash_buyer?: boolean;
  assumable_loan?: boolean;
  reo?: boolean;
  quit_claim?: boolean;
  flipped_min_times?: number;
  flipped_within_years?: number;
  
  // Construction/features
  construction_type?: string;
  heating_type?: string;
  cooling_type?: string;
  pool?: boolean;
  garage?: boolean;
  basement?: boolean;
  waterfront?: boolean;
  
  // Multi-family
  mfh_2to4?: boolean;
  
  // Special modes
  count?: boolean;
  summary?: boolean;
  ids_only?: boolean;
  exclude?: string[];
  
  // Profile and pagination
  searchProfile?: string;
  sort?: Record<string, string>;
  resultIndex?: number;
  size?: number;
  userId?: string;
}

/**
 * CMS Property Search API
 * 
 * An improved version of the property search API with better parameter handling and error management.
 * This is specifically designed for the CMS section of the application.
 * 
 * @route POST /api/property-search
 */
export async function POST(request: NextRequest) {
  // Check for API key
  const REAL_ESTATE_API_KEY = process.env.REAL_ESTATE_API_KEY;
  if (!REAL_ESTATE_API_KEY) {
    console.error('Error: REAL_ESTATE_API_KEY is not set in environment variables.');
    return NextResponse.json(
      { 
        success: false,
        statusCode: 500,
        message: 'API key is not configured.' 
      },
      { status: 500 }
    );
  }

  // Parse request body
  let requestBody: PropertySearchRequest;
  try {
    requestBody = await request.json();
  } catch (error) {
    console.error('Error parsing request JSON:', error);
    return NextResponse.json(
      { 
        success: false,
        statusCode: 400,
        message: 'Invalid JSON in request body.' 
      },
      { status: 400 }
    );
  }
  
  // Process request parameters
  let searchParams: Record<string, any> = {};
  
  // Handle geographic parameters
  if (requestBody.city) searchParams.city = requestBody.city;
  if (requestBody.state) searchParams.state = requestBody.state;
  if (requestBody.zip) searchParams.zip = requestBody.zip;
  if (requestBody.address) searchParams.address = requestBody.address;
  if (requestBody.county) searchParams.county = requestBody.county;
  if (requestBody.latitude && requestBody.longitude) {
    searchParams.latitude = requestBody.latitude;
    searchParams.longitude = requestBody.longitude;
    
    // Ensure radius is provided for coordinate searches
    if (requestBody.radius) {
      searchParams.radius = requestBody.radius;
    } else {
      searchParams.radius = 10; // Default radius of 10 miles
    }
  }
  
  // Handle property type filters
  if (requestBody.property_type) searchParams.property_type = requestBody.property_type;
  // For backward compatibility
  else if (requestBody.propertyType) searchParams.property_type = requestBody.propertyType;
  
  // Handle numeric range filters - Property Details
  if (requestBody.beds_min !== undefined) searchParams.beds_min = requestBody.beds_min;
  if (requestBody.beds_max !== undefined) searchParams.beds_max = requestBody.beds_max;
  if (requestBody.baths_min !== undefined) searchParams.baths_min = requestBody.baths_min;
  if (requestBody.baths_max !== undefined) searchParams.baths_max = requestBody.baths_max;
  if (requestBody.building_size_min !== undefined) searchParams.building_size_min = requestBody.building_size_min;
  if (requestBody.building_size_max !== undefined) searchParams.building_size_max = requestBody.building_size_max;
  if (requestBody.lot_size_min !== undefined) searchParams.lot_size_min = requestBody.lot_size_min;
  if (requestBody.lot_size_max !== undefined) searchParams.lot_size_max = requestBody.lot_size_max;
  if (requestBody.year_built_min !== undefined) searchParams.year_built_min = requestBody.year_built_min;
  if (requestBody.year_built_max !== undefined) searchParams.year_built_max = requestBody.year_built_max;
  if (requestBody.units_min !== undefined) searchParams.units_min = requestBody.units_min;
  if (requestBody.units_max !== undefined) searchParams.units_max = requestBody.units_max;
  
  // Handle financial filters
  if (requestBody.value_min !== undefined) searchParams.value_min = requestBody.value_min;
  if (requestBody.value_max !== undefined) searchParams.value_max = requestBody.value_max;
  if (requestBody.equity_min !== undefined) searchParams.equity_min = requestBody.equity_min;
  if (requestBody.equity_max !== undefined) searchParams.equity_max = requestBody.equity_max;
  if (requestBody.sale_price_min !== undefined) searchParams.sale_price_min = requestBody.sale_price_min;
  if (requestBody.sale_price_max !== undefined) searchParams.sale_price_max = requestBody.sale_price_max;
  if (requestBody.sale_date_min !== undefined) searchParams.sale_date_min = requestBody.sale_date_min;
  if (requestBody.sale_date_max !== undefined) searchParams.sale_date_max = requestBody.sale_date_max;
  
  // Handle MLS parameters
  if (requestBody.mls_active !== undefined) searchParams.mls_active = requestBody.mls_active;
  else if (requestBody.mlsActive !== undefined) searchParams.mls_active = requestBody.mlsActive;
  
  if (requestBody.mls_pending !== undefined) searchParams.mls_pending = requestBody.mls_pending;
  else if (requestBody.mlsPending !== undefined) searchParams.mls_pending = requestBody.mlsPending;
  
  if (requestBody.mls_sold !== undefined) searchParams.mls_sold = requestBody.mls_sold;
  else if (requestBody.mlsSold !== undefined) searchParams.mls_sold = requestBody.mlsSold;
  
  if (requestBody.mls_days_on_market_min !== undefined) searchParams.mls_days_on_market_min = requestBody.mls_days_on_market_min;
  if (requestBody.mls_days_on_market_max !== undefined) searchParams.mls_days_on_market_max = requestBody.mls_days_on_market_max;
  
  // Handle ownership filters
  if (requestBody.absentee_owner !== undefined) searchParams.absentee_owner = requestBody.absentee_owner;
  if (requestBody.corporate_owned !== undefined) searchParams.corporate_owned = requestBody.corporate_owned;
  if (requestBody.trust_owned !== undefined) searchParams.trust_owned = requestBody.trust_owned;
  if (requestBody.llc_owned !== undefined) searchParams.llc_owned = requestBody.llc_owned;
  if (requestBody.owner_occupied !== undefined) searchParams.owner_occupied = requestBody.owner_occupied;
  if (requestBody.ownership_length_min !== undefined) searchParams.ownership_length_min = requestBody.ownership_length_min;
  if (requestBody.ownership_length_max !== undefined) searchParams.ownership_length_max = requestBody.ownership_length_max;
  
  // Handle property condition filters
  if (requestBody.vacant !== undefined) searchParams.vacant = requestBody.vacant;
  if (requestBody.distressed !== undefined) searchParams.distressed = requestBody.distressed;
  if (requestBody.foreclosure !== undefined) searchParams.foreclosure = requestBody.foreclosure;
  if (requestBody.pre_foreclosure !== undefined) searchParams.pre_foreclosure = requestBody.pre_foreclosure;
  if (requestBody.auction !== undefined) searchParams.auction = requestBody.auction;
  if (requestBody.tax_lien !== undefined) searchParams.tax_lien = requestBody.tax_lien;
  if (requestBody.code_violation !== undefined) searchParams.code_violation = requestBody.code_violation;
  if (requestBody.flood_zone !== undefined) searchParams.flood_zone = requestBody.flood_zone;
  
  // Handle investment filters
  if (requestBody.high_equity !== undefined) searchParams.high_equity = requestBody.high_equity;
  if (requestBody.negative_equity !== undefined) searchParams.negative_equity = requestBody.negative_equity;
  if (requestBody.free_clear !== undefined) searchParams.free_clear = requestBody.free_clear;
  if (requestBody.cash_buyer !== undefined) searchParams.cash_buyer = requestBody.cash_buyer;
  if (requestBody.assumable_loan !== undefined) searchParams.assumable_loan = requestBody.assumable_loan;
  if (requestBody.reo !== undefined) searchParams.reo = requestBody.reo;
  if (requestBody.quit_claim !== undefined) searchParams.quit_claim = requestBody.quit_claim;
  if (requestBody.flipped_min_times !== undefined) searchParams.flipped_min_times = requestBody.flipped_min_times;
  if (requestBody.flipped_within_years !== undefined) searchParams.flipped_within_years = requestBody.flipped_within_years;
  
  // Handle construction/feature filters
  if (requestBody.construction_type !== undefined) searchParams.construction_type = requestBody.construction_type;
  if (requestBody.heating_type !== undefined) searchParams.heating_type = requestBody.heating_type;
  if (requestBody.cooling_type !== undefined) searchParams.cooling_type = requestBody.cooling_type;
  if (requestBody.pool !== undefined) searchParams.pool = requestBody.pool;
  if (requestBody.garage !== undefined) searchParams.garage = requestBody.garage;
  if (requestBody.basement !== undefined) searchParams.basement = requestBody.basement;
  if (requestBody.waterfront !== undefined) searchParams.waterfront = requestBody.waterfront;
  
  // Handle multi-family specific filters
  if (requestBody.mfh_2to4 !== undefined) searchParams.mfh_2to4 = requestBody.mfh_2to4;
  
  // Handle special search modes
  if (requestBody.count !== undefined) searchParams.count = requestBody.count;
  if (requestBody.summary !== undefined) searchParams.summary = requestBody.summary;
  if (requestBody.ids_only !== undefined) searchParams.ids_only = requestBody.ids_only;
  
  // Handle exclude field
  if (requestBody.exclude) searchParams.exclude = requestBody.exclude;
  
  // Apply search profile if requested
  if (requestBody.searchProfile && requestBody.searchProfile in ALL_SEARCH_PROFILES) {
    const profileParams = ALL_SEARCH_PROFILES[requestBody.searchProfile].searchParameters;
    searchParams = { ...searchParams, ...profileParams };
    console.log(`Applied search profile: ${requestBody.searchProfile}`);
  }
  
  // Handle sorting
  if (requestBody.sort) {
    searchParams.sort = requestBody.sort;
  }
  
  // Handle pagination
  searchParams.resultIndex = requestBody.resultIndex || 0;
  searchParams.size = requestBody.size || 25; // Default to 25 results per page
  
  // Validate that at least one search criteria is provided
  const hasGeoParam = searchParams.city || searchParams.state || searchParams.zip || searchParams.county || 
                      (searchParams.latitude && searchParams.longitude);
  const hasFilterParam = Object.keys(searchParams).some(key => 
    !['resultIndex', 'size', 'sort', 'count', 'summary', 'ids_only'].includes(key)
  );
  
  if (!hasGeoParam && !hasFilterParam) {
    return NextResponse.json(
      {
        success: false,
        statusCode: 400,
        message: 'At least one search criteria must be provided (location or property filter)'
      },
      { status: 400 }
    );
  }
  
  // Set up headers for the external API request
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'X-API-KEY': REAL_ESTATE_API_KEY
  };
  
  // Add optional user ID header if provided
  if (requestBody.userId) {
    headers['x-user-id'] = requestBody.userId;
  }
  
  // Log search parameters
  console.log('Searching with parameters:', JSON.stringify(searchParams, null, 2));
  
  try {
    // Set up request timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout
    
    // Make request to external API
    const response = await fetch(EXTERNAL_API_URL, {
      method: 'POST',
      headers,
      body: JSON.stringify(searchParams),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    // Check for successful response
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error: ${response.status} - ${errorText}`);
      
      return NextResponse.json(
        {
          success: false,
          statusCode: response.status,
          message: `Property search API error: ${response.statusText}`
        },
        { status: response.status }
      );
    }
    
    // Parse and return response data
    const data = await response.json();
    
    // Add pagination info
    if (data.resultCount > 0) {
      const resultIndex = searchParams.resultIndex || 0;
      const size = searchParams.size || 25;
      
      data.pagination = {
        currentPage: Math.floor(resultIndex / size) + 1,
        totalPages: Math.ceil(data.resultCount / size),
        pageSize: size,
        totalResults: data.resultCount,
        hasMore: resultIndex + size < data.resultCount
      };
    }
    
    return NextResponse.json({
      success: true,
      data
    });
    
  } catch (error: any) {
    // Handle request timeout
    if (error.name === 'AbortError') {
      return NextResponse.json(
        {
          success: false,
          statusCode: 504,
          message: 'Property search API request timed out'
        },
        { status: 504 }
      );
    }
    
    // Handle other errors
    console.error('Property search API error:', error);
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * Get search profiles
 * 
 * Returns a list of pre-configured search profiles for common Section 8 and investment scenarios.
 * 
 * @route GET /api/property-search
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const tag = searchParams.get('tag');
    
    let profiles: Record<string, SearchProfile> = ALL_SEARCH_PROFILES;
    
    // Filter by profile type if specified
    if (type === 'section8') {
      profiles = SECTION8_SEARCH_PROFILES;
    } else if (type === 'investment') {
      profiles = INVESTMENT_SEARCH_PROFILES;
    }
    
    // Filter by tag if specified
    if (tag) {
      const filteredProfiles: Record<string, SearchProfile> = {};
      
      Object.entries(profiles).forEach(([id, profile]) => {
        if (profile.tags && profile.tags.includes(tag)) {
          filteredProfiles[id] = profile;
        }
      });
      
      profiles = filteredProfiles;
    }
    
    // Transform to array format for client
    const profilesArray = Object.entries(profiles).map(([id, profile]) => ({
      id,
      name: profile.name,
      description: profile.description,
      tags: profile.tags || [],
      searchParameters: profile.searchParameters
    }));
    
    return NextResponse.json({
      success: true,
      profiles: profilesArray
    });
    
  } catch (error: any) {
    console.error('Error fetching search profiles:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}