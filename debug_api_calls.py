#!/usr/bin/env python3
"""
Debug API Calls - Show exact API requests being made
"""

import asyncio
import aiohttp
import os
from dotenv import load_dotenv

async def debug_real_estate_api():
    """Debug the Real Estate API calls with exact parameters"""
    print("🔍 Real Estate API Call Debug")
    print("=" * 60)
    
    load_dotenv()
    
    # API configuration
    api_key = os.getenv('REAL_ESTATE_API_KEY')
    base_url = os.getenv('REAL_ESTATE_API_URL')
    
    print(f"🌐 Base URL: {base_url}")
    print(f"🔑 API Key: {api_key[:10]}...{api_key[-10:]}")
    print()
    
    # Test different endpoint variations
    endpoints_to_test = [
        "/v2/PropertySearch",
        "/v1/PropertySearch", 
        "/PropertySearch",
        "/property/search",
        "/properties/search",
        "/search",
        "/api/v1/properties",
        "/api/v2/properties"
    ]
    
    # Sample parameters for Detroit, MI
    test_params = {
        "city": "DETROIT",
        "state": "MI",
        "bedrooms_min": 2,
        "bedrooms_max": 5,
        "price_min": 30000,
        "price_max": 400000,
        "days_on_market_max": 1,
        "property_types": "SINGLE_FAMILY,MULTI_FAMILY,TOWNHOUSE,DUPLEX",
        "limit": 25
    }
    
    # Alternative parameter formats to try
    param_variations = [
        # Original format
        test_params,
        
        # Alternative format 1
        {
            "location": "Detroit, MI",
            "min_bedrooms": 2,
            "max_bedrooms": 5,
            "min_price": 30000,
            "max_price": 400000,
            "max_days_on_market": 1,
            "property_type": "single_family,multi_family,townhouse,duplex",
            "limit": 25
        },
        
        # Alternative format 2
        {
            "q": "Detroit MI",
            "beds_min": 2,
            "beds_max": 5,
            "price_min": 30000,
            "price_max": 400000,
            "status": "for_sale",
            "limit": 25
        },
        
        # Alternative format 3
        {
            "address": "Detroit, MI",
            "minBeds": 2,
            "maxBeds": 5,
            "minPrice": 30000,
            "maxPrice": 400000,
            "propertyType": "single-family,multi-family",
            "limit": 25
        }
    ]
    
    headers_variations = [
        # Original headers
        {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        },
        
        # Alternative headers 1
        {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        },
        
        # Alternative headers 2
        {
            'Authorization': f'Token {api_key}',
            'Content-Type': 'application/json'
        },
        
        # Alternative headers 3
        {
            'apikey': api_key,
            'Content-Type': 'application/json'
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        print("🧪 Testing different endpoint and parameter combinations:")
        print()
        
        for endpoint in endpoints_to_test:
            print(f"📍 Testing endpoint: {endpoint}")
            
            for i, params in enumerate(param_variations):
                for j, headers in enumerate(headers_variations):
                    url = f"{base_url}{endpoint}"
                    
                    try:
                        print(f"  🔄 Attempt {i+1}.{j+1}: {url}")
                        print(f"     Headers: {list(headers.keys())}")
                        print(f"     Params: {len(params)} parameters")
                        
                        async with session.get(url, params=params, headers=headers, timeout=10) as response:
                            print(f"     ✅ Status: {response.status}")
                            
                            if response.status == 200:
                                try:
                                    data = await response.json()
                                    if isinstance(data, dict):
                                        print(f"     📊 Response keys: {list(data.keys())}")
                                        if 'properties' in data:
                                            print(f"     🏠 Properties found: {len(data['properties'])}")
                                        elif 'results' in data:
                                            print(f"     🏠 Results found: {len(data['results'])}")
                                        elif 'data' in data:
                                            print(f"     🏠 Data items: {len(data['data'])}")
                                    else:
                                        print(f"     📊 Response type: {type(data)}")
                                except:
                                    text = await response.text()
                                    print(f"     📄 Response length: {len(text)} chars")
                                
                                print("     🎉 SUCCESS! This combination works!")
                                return True
                            
                            elif response.status == 401:
                                print(f"     🔐 Authentication failed")
                            elif response.status == 403:
                                print(f"     🚫 Forbidden - check API key permissions")
                            elif response.status == 404:
                                print(f"     ❌ Endpoint not found")
                            elif response.status == 429:
                                print(f"     ⏰ Rate limited")
                            else:
                                print(f"     ⚠️ Unexpected status")
                                
                    except asyncio.TimeoutError:
                        print(f"     ⏱️ Request timed out")
                    except Exception as e:
                        print(f"     💥 Error: {str(e)[:50]}...")
                    
                    print()
            
            print("-" * 40)
    
    print("❌ No working combination found")
    print()
    print("🔧 TROUBLESHOOTING SUGGESTIONS:")
    print("1. Check if the API key is valid and active")
    print("2. Verify the base URL is correct")
    print("3. Check API documentation for correct endpoints")
    print("4. Confirm the API supports the required parameters")
    print("5. Test with a simple API client like Postman")
    print()
    print("💡 ALTERNATIVE SOLUTIONS:")
    print("1. Use a different real estate data provider")
    print("2. Implement web scraping (with proper permissions)")
    print("3. Use public MLS data feeds")
    print("4. Partner with a real estate data company")
    
    return False

async def debug_hud_api():
    """Debug the HUD API calls"""
    print("\n🏛️ HUD API Call Debug")
    print("=" * 60)
    
    load_dotenv()
    
    hud_api_key = os.getenv('HUD_API_KEY')
    hud_api_url = os.getenv('HUD_API_URL')
    
    print(f"🌐 HUD URL: {hud_api_url}")
    print(f"🔑 HUD Key: {hud_api_key[:10]}...{hud_api_key[-10:]}")
    print()
    
    # Test HUD API endpoints
    test_endpoints = [
        "/fmr/listCounties/MI",
        "/fmr/listCounties/OH", 
        "/fmr/data/MI",
        "/fmr/data/OH"
    ]
    
    headers = {
        'Authorization': f'Bearer {hud_api_key}',
        'Content-Type': 'application/json'
    }
    
    async with aiohttp.ClientSession() as session:
        for endpoint in test_endpoints:
            url = f"{hud_api_url}{endpoint}"
            
            try:
                print(f"🔄 Testing: {url}")
                async with session.get(url, headers=headers, timeout=10) as response:
                    print(f"   Status: {response.status}")
                    
                    if response.status == 200:
                        try:
                            data = await response.json()
                            print(f"   ✅ Success! Data keys: {list(data.keys()) if isinstance(data, dict) else 'List data'}")
                        except:
                            text = await response.text()
                            print(f"   📄 Response length: {len(text)} chars")
                    else:
                        print(f"   ❌ Failed")
                        
            except Exception as e:
                print(f"   💥 Error: {str(e)[:50]}...")
            
            print()

if __name__ == "__main__":
    asyncio.run(debug_real_estate_api())
    asyncio.run(debug_hud_api())
