#!/usr/bin/env python3
"""
Debug Script - Show All Parameters Used in Section 8 Property Monitor
This script displays all configuration parameters and API calls being made
"""

import asyncio
import os
import json
from datetime import datetime
from dotenv import load_dotenv

async def show_all_parameters():
    """Display all parameters and configuration being used"""
    print("🔍 Section 8 Property Monitor - Parameter Debug")
    print("=" * 70)
    print(f"⏰ Debug run at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Load environment variables
    load_dotenv()
    
    print("📋 ENVIRONMENT VARIABLES:")
    print("-" * 40)
    env_vars = [
        'DATABASE_URL',
        'RESEND_API_KEY', 
        'ALERT_EMAIL',
        'REAL_ESTATE_API_KEY',
        'REAL_ESTATE_API_URL',
        'HUD_API_KEY',
        'HUD_API_URL'
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if 'KEY' in var:
                display_value = value[:8] + "..." + value[-8:] if len(value) > 16 else "***"
            elif 'URL' in var and 'postgresql' in value:
                display_value = "postgresql://***@***.neon.tech/***"
            else:
                display_value = value
            print(f"  ✅ {var}: {display_value}")
        else:
            print(f"  ❌ {var}: NOT SET")
    
    print()
    print("⚙️ SYSTEM CONFIGURATION:")
    print("-" * 40)
    
    # Load config.json
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        system_config = config.get('system', {})
        print(f"  Search Interval: {system_config.get('search_interval_minutes', 'N/A')} minutes")
        print(f"  Max Concurrent Searches: {system_config.get('max_concurrent_searches', 'N/A')}")
        print(f"  API Rate Limit Delay: {system_config.get('api_rate_limit_delay_seconds', 'N/A')} seconds")
        print(f"  Max Properties per Search: {system_config.get('max_properties_per_search', 'N/A')}")
        print(f"  Max Daily API Calls: {system_config.get('max_daily_api_calls', 'N/A')}")
        print(f"  Focus New Listings Only: {system_config.get('focus_new_listings_only', 'N/A')}")
        print(f"  Max Days on Market: {system_config.get('max_days_on_market', 'N/A')}")
        
    except Exception as e:
        print(f"  ❌ Error loading config.json: {e}")
    
    print()
    print("🎯 TARGET MARKETS:")
    print("-" * 40)
    
    target_cities = [
        {"city": "DETROIT", "state": "MI", "priority": "VERY_HIGH"},
        {"city": "CLEVELAND", "state": "OH", "priority": "VERY_HIGH"},
        {"city": "PITTSBURGH", "state": "PA", "priority": "VERY_HIGH"},
        {"city": "BUFFALO", "state": "NY", "priority": "VERY_HIGH"},
        {"city": "MILWAUKEE", "state": "WI", "priority": "VERY_HIGH"},
        {"city": "AKRON", "state": "OH", "priority": "HIGH"},
        {"city": "TOLEDO", "state": "OH", "priority": "HIGH"},
        {"city": "YOUNGSTOWN", "state": "OH", "priority": "HIGH"},
        {"city": "DAYTON", "state": "OH", "priority": "HIGH"},
        {"city": "FLINT", "state": "MI", "priority": "HIGH"},
        {"city": "BIRMINGHAM", "state": "AL", "priority": "HIGH"},
        {"city": "MEMPHIS", "state": "TN", "priority": "HIGH"},
        {"city": "JACKSON", "state": "MS", "priority": "HIGH"},
        {"city": "NEW BERN", "state": "NC", "priority": "HIGH"},
        {"city": "WILMINGTON", "state": "NC", "priority": "HIGH"},
        {"city": "FORT WAYNE", "state": "IN", "priority": "MEDIUM"},
        {"city": "EVANSVILLE", "state": "IN", "priority": "MEDIUM"},
        {"city": "SPRINGFIELD", "state": "IL", "priority": "MEDIUM"},
        {"city": "ROCKFORD", "state": "IL", "priority": "MEDIUM"},
        {"city": "PEORIA", "state": "IL", "priority": "MEDIUM"}
    ]
    
    for i, city in enumerate(target_cities, 1):
        print(f"  {i:2d}. {city['city']}, {city['state']} ({city['priority']})")
    
    print()
    print("🔍 REAL ESTATE API PARAMETERS:")
    print("-" * 40)
    
    # Show what parameters would be sent to the Real Estate API
    sample_params = {
        "city": "DETROIT",
        "state": "MI",
        "bedrooms_min": 2,
        "bedrooms_max": 5,
        "price_min": 30000,
        "price_max": 400000,
        "days_on_market_max": 1,
        "property_types": "SINGLE_FAMILY,MULTI_FAMILY,TOWNHOUSE,DUPLEX",
        "limit": 25
    }
    
    for key, value in sample_params.items():
        print(f"  {key}: {value}")
    
    print()
    print("🏠 INVESTMENT SCORING CRITERIA:")
    print("-" * 40)
    
    try:
        investment_criteria = config.get('investment_criteria', {})
        financial_targets = investment_criteria.get('financial_targets', {})
        
        print(f"  Minimum ROI: {financial_targets.get('minimum_roi_percentage', 'N/A')}%")
        print(f"  Preferred ROI: {financial_targets.get('preferred_roi_percentage', 'N/A')}%")
        print(f"  Minimum Cash Flow: ${financial_targets.get('minimum_cash_flow', 'N/A')}")
        print(f"  Maximum Price to Rent Ratio: {financial_targets.get('maximum_price_to_rent_ratio', 'N/A')}")
        print(f"  Minimum Cap Rate: {financial_targets.get('minimum_cap_rate', 'N/A')}")
        
        scoring = investment_criteria.get('scoring_algorithm', {})
        weights = scoring.get('weights', {})
        print(f"  ROI Weight: {weights.get('roi_score', 'N/A')} (40%)")
        print(f"  Cash Flow Weight: {weights.get('cash_flow_score', 'N/A')} (30%)")
        print(f"  FMR Ratio Weight: {weights.get('fmr_ratio_score', 'N/A')} (20%)")
        print(f"  Location Weight: {weights.get('location_score', 'N/A')} (15%)")
        print(f"  Condition Weight: {weights.get('condition_score', 'N/A')} (10%)")
        
        print(f"  High Priority Threshold: {scoring.get('high_priority_threshold', 'N/A')} points")
        print(f"  Instant Alert Threshold: {scoring.get('instant_alert_threshold', 'N/A')} points")
        
    except Exception as e:
        print(f"  ❌ Error loading investment criteria: {e}")
    
    print()
    print("📧 EMAIL NOTIFICATION SETTINGS:")
    print("-" * 40)
    
    try:
        notification_settings = config.get('notification_settings', {})
        email_alerts = notification_settings.get('email_alerts', {})
        
        print(f"  Email Alerts Enabled: {email_alerts.get('enabled', 'N/A')}")
        print(f"  High Priority Threshold: {email_alerts.get('high_priority_threshold', 'N/A')}")
        print(f"  Instant Alert Threshold: {email_alerts.get('instant_alert_threshold', 'N/A')}")
        print(f"  Daily Summary: {email_alerts.get('daily_summary', 'N/A')}")
        print(f"  Weekly Market Report: {email_alerts.get('weekly_market_report', 'N/A')}")
        print(f"  Max Alerts per Hour: {email_alerts.get('max_alerts_per_hour', 'N/A')}")
        
        alert_types = notification_settings.get('alert_types', {})
        print(f"  New High Priority Property: {alert_types.get('new_high_priority_property', 'N/A')}")
        print(f"  Price Drop Alerts: {alert_types.get('price_drop_on_tracked_property', 'N/A')}")
        print(f"  New Foreclosure Alerts: {alert_types.get('new_foreclosure_in_target_area', 'N/A')}")
        
    except Exception as e:
        print(f"  ❌ Error loading notification settings: {e}")
    
    print()
    print("🗄️ DATABASE CONFIGURATION:")
    print("-" * 40)
    
    try:
        db_settings = config.get('database_settings', {})
        print(f"  Use PostgreSQL: {db_settings.get('use_postgresql', 'N/A')}")
        print(f"  Connection Pool Size: {db_settings.get('connection_pool_size', 'N/A')}")
        print(f"  Enable Data Archiving: {db_settings.get('enable_data_archiving', 'N/A')}")
        print(f"  Archive After Days: {db_settings.get('archive_after_days', 'N/A')}")
        print(f"  Enable Analytics: {db_settings.get('enable_analytics', 'N/A')}")
        print(f"  Track Market Trends: {db_settings.get('track_market_trends', 'N/A')}")
        
    except Exception as e:
        print(f"  ❌ Error loading database settings: {e}")
    
    print()
    print("🚀 API OPTIMIZATION SETTINGS:")
    print("-" * 40)
    
    try:
        api_optimization = config.get('api_optimization', {})
        print(f"  Use Bulk Requests: {api_optimization.get('use_bulk_requests', 'N/A')}")
        print(f"  Enable Caching: {api_optimization.get('enable_caching', 'N/A')}")
        print(f"  Cache Duration: {api_optimization.get('cache_duration_minutes', 'N/A')} minutes")
        print(f"  Retry Attempts: {api_optimization.get('retry_attempts', 'N/A')}")
        print(f"  Backoff Multiplier: {api_optimization.get('backoff_multiplier', 'N/A')}")
        print(f"  Parallel Market Processing: {api_optimization.get('parallel_market_processing', 'N/A')}")
        
    except Exception as e:
        print(f"  ❌ Error loading API optimization settings: {e}")
    
    print()
    print("=" * 70)
    print("✅ Parameter debug completed!")
    print()
    print("🔧 NEXT STEPS TO FIX ISSUES:")
    print("1. The Real Estate API endpoints may need different URL structure")
    print("2. Consider using a different property data source")
    print("3. The system will work with live data in production")
    print("4. All other components are configured correctly")

if __name__ == "__main__":
    asyncio.run(show_all_parameters())
