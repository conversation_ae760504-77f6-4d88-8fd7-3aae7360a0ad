#!/usr/bin/env python3
"""
Section 8 Property Monitor - Final Version
Advanced nationwide monitoring system with HUD integration and PostgreSQL database
"""

import asyncio
import asyncpg
import aiohttp
import json
import logging
import os
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from decimal import Decimal
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import traceback
from pathlib import Path
import uuid

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

@dataclass
class PropertyLead:
    """Data class for property investment leads"""
    id: str
    address: str
    city: str
    state: str
    zip_code: str
    property_type: str
    bedrooms: int
    bathrooms: float
    square_feet: int
    year_built: int
    estimated_value: int
    estimated_rent: int
    fair_market_rent: int
    cash_flow: int
    roi_percentage: float
    cap_rate: float
    score: int
    priority: str
    fmr_ratio: float
    market_tier: str
    discovered_at: datetime
    source: str
    
@dataclass
class MarketAnalysis:
    """Market analysis data structure"""
    city: str
    state: str
    avg_fmr_2br: int
    avg_fmr_3br: int
    avg_property_value: int
    total_properties_found: int
    high_priority_count: int
    market_score: float
    last_updated: datetime

class Section8MonitorPro:
    """Advanced Section 8 property monitoring system"""
    
    def __init__(self):
        self.config = self.load_config()
        self.setup_logging()
        self.session: Optional[aiohttp.ClientSession] = None
        self.db_pool: Optional[asyncpg.Pool] = None
        self.running = False
        
        # API endpoints
        self.real_estate_api_url = os.getenv('REAL_ESTATE_API_URL')
        self.real_estate_api_key = os.getenv('REAL_ESTATE_API_KEY')
        self.hud_api_key = os.getenv('HUD_API_KEY')
        self.resend_api_key = os.getenv('RESEND_API_KEY')
        self.alert_email = os.getenv('ALERT_EMAIL')
        
        # Database connection
        self.database_url = os.getenv('DATABASE_URL')
        
        # Performance tracking
        self.daily_stats = {
            'properties_analyzed': 0,
            'high_priority_found': 0,
            'alerts_sent': 0,
            'api_calls_made': 0,
            'markets_processed': 0
        }
        
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from config.json"""
        try:
            with open('config.json', 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            return {}
    
    def setup_logging(self):
        """Configure comprehensive logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('section8_monitor_pro.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self):
        """Initialize database and HTTP session"""
        try:
            # Initialize HTTP session
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # Initialize database connection pool
            self.db_pool = await asyncpg.create_pool(
                self.database_url,
                min_size=5,
                max_size=self.config.get('database_settings', {}).get('connection_pool_size', 25)
            )
            
            # Setup database schema
            await self.setup_database_schema()
            
            self.logger.info("Section 8 Monitor Pro initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize: {e}")
            raise
    
    async def setup_database_schema(self):
        """Create database tables if they don't exist"""
        schema_sql = """
        -- Property leads table
        CREATE TABLE IF NOT EXISTS property_leads (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            property_api_id TEXT,
            address TEXT NOT NULL,
            city TEXT NOT NULL,
            state TEXT NOT NULL,
            zip_code TEXT,
            property_type TEXT,
            bedrooms INTEGER,
            bathrooms DECIMAL,
            square_feet INTEGER,
            year_built INTEGER,
            estimated_value INTEGER,
            estimated_rent INTEGER,
            fair_market_rent INTEGER,
            cash_flow INTEGER,
            roi_percentage DECIMAL,
            cap_rate DECIMAL,
            score INTEGER,
            priority TEXT,
            fmr_ratio DECIMAL,
            market_tier TEXT,
            discovered_at TIMESTAMP DEFAULT NOW(),
            last_updated TIMESTAMP DEFAULT NOW(),
            source TEXT,
            raw_data JSONB,
            UNIQUE(property_api_id)
        );
        
        -- Market analysis table
        CREATE TABLE IF NOT EXISTS market_analysis (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            city TEXT NOT NULL,
            state TEXT NOT NULL,
            avg_fmr_2br INTEGER,
            avg_fmr_3br INTEGER,
            avg_property_value INTEGER,
            total_properties_found INTEGER,
            high_priority_count INTEGER,
            market_score DECIMAL,
            last_updated TIMESTAMP DEFAULT NOW(),
            analysis_data JSONB,
            UNIQUE(city, state)
        );
        
        -- Daily statistics table
        CREATE TABLE IF NOT EXISTS daily_statistics (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            date DATE NOT NULL,
            properties_analyzed INTEGER DEFAULT 0,
            high_priority_found INTEGER DEFAULT 0,
            alerts_sent INTEGER DEFAULT 0,
            api_calls_made INTEGER DEFAULT 0,
            markets_processed INTEGER DEFAULT 0,
            execution_time_seconds INTEGER,
            created_at TIMESTAMP DEFAULT NOW(),
            UNIQUE(date)
        );
        
        -- Search history table
        CREATE TABLE IF NOT EXISTS search_history (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            city TEXT,
            state TEXT,
            search_parameters JSONB,
            results_count INTEGER,
            high_priority_count INTEGER,
            execution_time_ms INTEGER,
            created_at TIMESTAMP DEFAULT NOW()
        );
        
        -- Alerts log table
        CREATE TABLE IF NOT EXISTS alerts_log (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            property_id UUID REFERENCES property_leads(id),
            alert_type TEXT,
            recipient TEXT,
            sent_at TIMESTAMP DEFAULT NOW(),
            status TEXT,
            error_message TEXT
        );
        
        -- Create indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_property_leads_score ON property_leads(score DESC);
        CREATE INDEX IF NOT EXISTS idx_property_leads_priority ON property_leads(priority);
        CREATE INDEX IF NOT EXISTS idx_property_leads_discovered ON property_leads(discovered_at DESC);
        CREATE INDEX IF NOT EXISTS idx_property_leads_city_state ON property_leads(city, state);
        CREATE INDEX IF NOT EXISTS idx_market_analysis_score ON market_analysis(market_score DESC);
        CREATE INDEX IF NOT EXISTS idx_search_history_created ON search_history(created_at DESC);
        """
        
        async with self.db_pool.acquire() as conn:
            await conn.execute(schema_sql)
        
        self.logger.info("Database schema initialized")
    
    async def get_top_section8_cities(self) -> List[Dict[str, Any]]:
        """Get top Section 8 cities using existing API"""
        try:
            # Use the existing top-section8-cities API endpoint
            async with self.session.get(f'{self.real_estate_api_url}/api/top-section8-cities') as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success') and data.get('topCities'):
                        return data['topCities']
                else:
                    self.logger.warning(f"Top cities API returned {response.status}")
        except Exception as e:
            self.logger.error(f"Failed to get top Section 8 cities: {e}")
        
        # Fallback to config-based cities
        all_markets = []
        for tier_name, cities in self.config.get('target_markets', {}).items():
            all_markets.extend(cities)
        
        return all_markets
    
    async def get_hud_fmr_data(self, city: str, state: str, zip_code: Optional[str] = None) -> Dict[str, Any]:
        """Get Fair Market Rent data from HUD API"""
        try:
            params = {
                'city': city,
                'state': state
            }
            if zip_code:
                params['zip'] = zip_code
            
            # Use existing HUD FMR API endpoint
            async with self.session.get(f'{self.real_estate_api_url}/api/hud/fmr', params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success') and data.get('data'):
                        return data['data'][0] if data['data'] else {}
                else:
                    self.logger.warning(f"HUD FMR API returned {response.status} for {city}, {state}")
        except Exception as e:
            self.logger.error(f"Failed to get HUD FMR data for {city}, {state}: {e}")
        
        return {}
    
    async def search_properties(self, market: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search for properties in a specific market using existing property search API"""
        try:
            # Get price range based on market tier
            tier_mapping = {
                'VERY_HIGH': 'tier_1_primary',
                'HIGH': 'tier_2_value',
                'MEDIUM': 'tier_3_growth'
            }
            
            tier_key = tier_mapping.get(market.get('priority', 'MEDIUM'), 'tier_2_value')
            price_range = self.config['investment_criteria']['price_ranges'][tier_key]
            
            # Build search parameters
            search_params = {
                'city': market['city'],
                'state': market['state'],
                'value_min': price_range['min'],
                'value_max': price_range['max'],
                'beds_min': self.config['investment_criteria']['property_specifications']['bedrooms_min'],
                'beds_max': self.config['investment_criteria']['property_specifications']['bedrooms_max'],
                'baths_min': self.config['investment_criteria']['property_specifications']['bathrooms_min'],
                'building_size_min': self.config['investment_criteria']['property_specifications']['square_feet_min'],
                'building_size_max': self.config['investment_criteria']['property_specifications']['square_feet_max'],
                'year_built_min': self.config['investment_criteria']['property_specifications']['year_built_min'],
                'property_type': self.config['investment_criteria']['property_specifications']['property_types'][0],
                'absentee_owner': self.config['investment_criteria']['investment_filters']['absentee_owner'],
                'vacant': self.config['investment_criteria']['investment_filters']['vacant'],
                'foreclosure': self.config['investment_criteria']['investment_filters']['foreclosure'],
                'size': self.config['system']['max_properties_per_search'],
                'searchProfile': 'affordable-sfr'  # Use existing Section 8 profile
            }
            
            headers = {
                'Content-Type': 'application/json',
                'X-API-KEY': self.real_estate_api_key
            }
            
            # Use existing property search API
            async with self.session.post(
                f'{self.real_estate_api_url}/api/property-search',
                json=search_params,
                headers=headers
            ) as response:
                
                if response.status == 200:
                    data = await response.json()
                    if data.get('success') and data.get('data', {}).get('data'):
                        self.daily_stats['api_calls_made'] += 1
                        return data['data']['data']
                else:
                    self.logger.warning(f"Property search API returned {response.status} for {market['city']}, {market['state']}")
                    
        except Exception as e:
            self.logger.error(f"Failed to search properties in {market['city']}, {market['state']}: {e}")
        
        return []
    
    async def get_property_details(self, property_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed property information using existing property detail API"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'X-API-KEY': self.real_estate_api_key
            }
            
            # Use existing property detail API
            async with self.session.post(
                f'{self.real_estate_api_url}/api/property-detail',
                json={'id': property_id, 'includeComps': True},
                headers=headers
            ) as response:
                
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        self.daily_stats['api_calls_made'] += 1
                        return data.get('data')
                else:
                    self.logger.warning(f"Property detail API returned {response.status} for property {property_id}")
                    
        except Exception as e:
            self.logger.error(f"Failed to get property details for {property_id}: {e}")
        
        return None
    
    def calculate_property_score(self, property_data: Dict[str, Any], fmr_data: Dict[str, Any], market: Dict[str, Any]) -> Tuple[int, str, Dict[str, Any]]:
        """Calculate comprehensive investment score for a property"""
        try:
            scoring_config = self.config['investment_criteria']['scoring_algorithm']
            weights = scoring_config['weights']
            
            # Extract property details
            bedrooms = property_data.get('bedrooms', 0)
            estimated_value = property_data.get('estimatedValue', 0)
            square_feet = property_data.get('squareFeet', 1000)
            year_built = property_data.get('yearBuilt', 1980)
            
            # Get Fair Market Rent based on bedrooms
            fmr_mapping = {
                1: fmr_data.get('fmr1', market.get('expected_fmr_2br', 800)),
                2: fmr_data.get('fmr2', market.get('expected_fmr_2br', 800)),
                3: fmr_data.get('fmr3', market.get('expected_fmr_3br', 1000)),
                4: fmr_data.get('fmr4', market.get('expected_fmr_3br', 1200) * 1.2),
                5: fmr_data.get('fmr4', market.get('expected_fmr_3br', 1200) * 1.4)
            }
            
            fair_market_rent = fmr_mapping.get(bedrooms, 800)
            
            # Estimate monthly expenses (30% of property value annually / 12)
            monthly_expenses = (estimated_value * 0.003) if estimated_value > 0 else 300
            
            # Calculate cash flow and ROI
            monthly_cash_flow = fair_market_rent - monthly_expenses
            annual_cash_flow = monthly_cash_flow * 12
            roi_percentage = (annual_cash_flow / estimated_value * 100) if estimated_value > 0 else 0
            cap_rate = (annual_cash_flow / estimated_value) if estimated_value > 0 else 0
            
            # Calculate component scores (0-100 each)
            
            # 1. ROI Score (30% weight)
            roi_score = min(roi_percentage * 5, 100) if roi_percentage > 0 else 0
            
            # 2. Cash Flow Score (25% weight)
            cash_flow_score = min(max(monthly_cash_flow / 5, 0), 100)
            
            # 3. FMR Ratio Score (20% weight) - Lower ratios are better
            fmr_ratio = fair_market_rent / (estimated_value / 100) if estimated_value > 0 else 0
            fmr_ratio_score = min(fmr_ratio * 10, 100)
            
            # 4. Location Score (15% weight) - Based on market priority
            location_multipliers = {'VERY_HIGH': 1.0, 'HIGH': 0.9, 'MEDIUM': 0.8, 'LOW': 0.7}
            location_score = 85 * location_multipliers.get(market.get('priority', 'MEDIUM'), 0.8)
            
            # 5. Condition Score (10% weight) - Based on year built and property characteristics
            age = 2024 - year_built
            age_score = max(100 - (age / 2), 20)  # Newer properties score higher
            
            # Add bonuses for investment-friendly characteristics
            bonus_points = 0
            if property_data.get('absenteeOwner'):
                bonus_points += 5
            if property_data.get('vacant'):
                bonus_points += 5
            if property_data.get('foreclosure'):
                bonus_points += 10
            if property_data.get('distressed'):
                bonus_points += 5
            
            condition_score = min(age_score + bonus_points, 100)
            
            # Calculate weighted total score
            total_score = (
                roi_score * weights['roi_score'] +
                cash_flow_score * weights['cash_flow_score'] +
                fmr_ratio_score * weights['fmr_ratio_score'] +
                location_score * weights['location_score'] +
                condition_score * weights['condition_score']
            )
            
            # Determine priority
            if total_score >= scoring_config['instant_alert_threshold']:
                priority = 'INSTANT'
            elif total_score >= scoring_config['high_priority_threshold']:
                priority = 'HIGH'
            elif total_score >= scoring_config['minimum_total_score']:
                priority = 'MEDIUM'
            else:
                priority = 'LOW'
            
            # Create analysis details
            analysis = {
                'fair_market_rent': fair_market_rent,
                'monthly_cash_flow': monthly_cash_flow,
                'roi_percentage': roi_percentage,
                'cap_rate': cap_rate,
                'fmr_ratio': fmr_ratio,
                'component_scores': {
                    'roi_score': roi_score,
                    'cash_flow_score': cash_flow_score,
                    'fmr_ratio_score': fmr_ratio_score,
                    'location_score': location_score,
                    'condition_score': condition_score
                },
                'bonus_points': bonus_points
            }
            
            return int(total_score), priority, analysis
            
        except Exception as e:
            self.logger.error(f"Error calculating property score: {e}")
            return 0, 'LOW', {}
    
    async def process_property_lead(self, property_data: Dict[str, Any], market: Dict[str, Any]) -> Optional[PropertyLead]:
        """Process a property and create a lead if it meets criteria"""
        try:
            # Get HUD FMR data for the property location
            fmr_data = await self.get_hud_fmr_data(
                property_data.get('city', market['city']),
                property_data.get('state', market['state']),
                property_data.get('zip')
            )
            
            # Calculate property score
            score, priority, analysis = self.calculate_property_score(property_data, fmr_data, market)
            
            # Only process properties that meet minimum criteria
            if score < self.config['investment_criteria']['scoring_algorithm']['minimum_total_score']:
                return None
            
            # Create property lead
            lead = PropertyLead(
                id=str(uuid.uuid4()),
                address=property_data.get('address', ''),
                city=property_data.get('city', market['city']),
                state=property_data.get('state', market['state']),
                zip_code=property_data.get('zip', ''),
                property_type=property_data.get('propertyType', 'Unknown'),
                bedrooms=property_data.get('bedrooms', 0),
                bathrooms=property_data.get('bathrooms', 0.0),
                square_feet=property_data.get('squareFeet', 0),
                year_built=property_data.get('yearBuilt', 0),
                estimated_value=property_data.get('estimatedValue', 0),
                estimated_rent=int(analysis.get('fair_market_rent', 0)),
                fair_market_rent=int(analysis.get('fair_market_rent', 0)),
                cash_flow=int(analysis.get('monthly_cash_flow', 0)),
                roi_percentage=analysis.get('roi_percentage', 0.0),
                cap_rate=analysis.get('cap_rate', 0.0),
                score=score,
                priority=priority,
                fmr_ratio=analysis.get('fmr_ratio', 0.0),
                market_tier=f"tier_{market.get('priority', 'medium').lower()}",
                discovered_at=datetime.now(),
                source='automated_search'
            )
            
            # Save to database
            await self.save_property_lead(lead, property_data, analysis)
            
            return lead
            
        except Exception as e:
            self.logger.error(f"Error processing property lead: {e}")
            return None
    
    async def save_property_lead(self, lead: PropertyLead, raw_data: Dict[str, Any], analysis: Dict[str, Any]):
        """Save property lead to database"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO property_leads (
                        id, property_api_id, address, city, state, zip_code, property_type,
                        bedrooms, bathrooms, square_feet, year_built, estimated_value,
                        estimated_rent, fair_market_rent, cash_flow, roi_percentage,
                        cap_rate, score, priority, fmr_ratio, market_tier, source, raw_data
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23)
                    ON CONFLICT (property_api_id) DO UPDATE SET
                        score = EXCLUDED.score,
                        priority = EXCLUDED.priority,
                        last_updated = NOW()
                """, 
                    lead.id,
                    raw_data.get('id', ''),
                    lead.address,
                    lead.city,
                    lead.state,
                    lead.zip_code,
                    lead.property_type,
                    lead.bedrooms,
                    lead.bathrooms,
                    lead.square_feet,
                    lead.year_built,
                    lead.estimated_value,
                    lead.estimated_rent,
                    lead.fair_market_rent,
                    lead.cash_flow,
                    lead.roi_percentage,
                    lead.cap_rate,
                    lead.score,
                    lead.priority,
                    lead.fmr_ratio,
                    lead.market_tier,
                    lead.source,
                    json.dumps({**raw_data, 'analysis': analysis})
                )
                
        except Exception as e:
            self.logger.error(f"Error saving property lead: {e}")
    
    async def send_alert_email(self, leads: List[PropertyLead]):
        """Send email alert for high-priority leads"""
        try:
            if not leads or not self.alert_email or not self.resend_api_key:
                return
            
            # Filter high priority leads
            high_priority_leads = [lead for lead in leads if lead.priority in ['HIGH', 'INSTANT']]
            
            if not high_priority_leads:
                return
            
            # Create email content
            subject = f"🏠 {len(high_priority_leads)} New Section 8 Investment Opportunities!"
            
            html_body = f"""
            <h2>Section 8 Property Alert - {datetime.now().strftime('%Y-%m-%d %H:%M')}</h2>
            <p>Found {len(high_priority_leads)} high-priority investment opportunities:</p>
            """
            
            for i, lead in enumerate(high_priority_leads[:10], 1):  # Limit to top 10
                html_body += f"""
                <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px;">
                    <h3>{i}. {lead.address}</h3>
                    <p><strong>Score:</strong> {lead.score}/100 | <strong>Priority:</strong> {lead.priority}</p>
                    <p><strong>Price:</strong> ${lead.estimated_value:,} | <strong>Rent:</strong> ${lead.fair_market_rent:,}/mo</p>
                    <p><strong>Cash Flow:</strong> ${lead.cash_flow:,}/mo | <strong>ROI:</strong> {lead.roi_percentage:.1f}%</p>
                    <p><strong>Details:</strong> {lead.bedrooms}BR/{lead.bathrooms}BA | {lead.square_feet:,} sq ft | Built {lead.year_built}</p>
                    <p><strong>Market:</strong> {lead.city}, {lead.state} | <strong>Tier:</strong> {lead.market_tier}</p>
                </div>
                """
            
            html_body += f"""
            <p><strong>Total Properties Analyzed Today:</strong> {self.daily_stats['properties_analyzed']}</p>
            <p><em>Generated by Section 8 Monitor Pro - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</em></p>
            """
            
            # Send via Resend API
            payload = {
                "from": "Section8Monitor <<EMAIL>>",
                "to": [self.alert_email],
                "subject": subject,
                "html": html_body
            }
            
            headers = {
                "Authorization": f"Bearer {self.resend_api_key}",
                "Content-Type": "application/json"
            }
            
            async with self.session.post("https://api.resend.com/emails", json=payload, headers=headers) as response:
                if response.status == 200:
                    self.daily_stats['alerts_sent'] += 1
                    self.logger.info(f"Email alert sent for {len(high_priority_leads)} properties")
                    
                    # Log alert in database
                    for lead in high_priority_leads:
                        await self.log_alert(lead.id, 'email', 'sent')
                else:
                    self.logger.error(f"Failed to send email alert: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"Error sending email alert: {e}")
    
    async def log_alert(self, property_id: str, alert_type: str, status: str, error_message: str = None):
        """Log alert in database"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO alerts_log (property_id, alert_type, recipient, status, error_message)
                    VALUES ($1, $2, $3, $4, $5)
                """, property_id, alert_type, self.alert_email, status, error_message)
        except Exception as e:
            self.logger.error(f"Error logging alert: {e}")
    
    async def process_market(self, market: Dict[str, Any]) -> List[PropertyLead]:
        """Process a single market for property opportunities"""
        start_time = time.time()
        leads = []
        
        try:
            self.logger.info(f"Processing market: {market['city']}, {market['state']} (Priority: {market.get('priority', 'MEDIUM')})")
            
            # Search for properties in this market
            properties = await self.search_properties(market)
            self.daily_stats['properties_analyzed'] += len(properties)
            
            if not properties:
                self.logger.info(f"No properties found in {market['city']}, {market['state']}")
                return leads
            
            # Process each property
            tasks = []
            for property_data in properties:
                task = self.process_property_lead(property_data, market)
                tasks.append(task)
            
            # Process properties concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Collect successful leads
            for result in results:
                if isinstance(result, PropertyLead):
                    leads.append(result)
                    if result.priority in ['HIGH', 'INSTANT']:
                        self.daily_stats['high_priority_found'] += 1
            
            # Log search history
            await self.log_search_history(market, len(properties), len(leads), int((time.time() - start_time) * 1000))
            
            self.logger.info(f"Market {market['city']}, {market['state']} processed: {len(properties)} properties → {len(leads)} leads")
            
        except Exception as e:
            self.logger.error(f"Error processing market {market['city']}, {market['state']}: {e}")
        
        return leads
    
    async def log_search_history(self, market: Dict[str, Any], results_count: int, high_priority_count: int, execution_time_ms: int):
        """Log search history to database"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO search_history (city, state, search_parameters, results_count, high_priority_count, execution_time_ms)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, market['city'], market['state'], json.dumps(market), results_count, high_priority_count, execution_time_ms)
        except Exception as e:
            self.logger.error(f"Error logging search history: {e}")
    
    async def run_monitoring_cycle(self):
        """Run a complete monitoring cycle across all markets"""
        start_time = time.time()
        all_leads = []
        
        try:
            self.logger.info("Starting monitoring cycle")
            
            # Get target markets (either from API or config)
            target_markets = await self.get_top_section8_cities()
            
            # If API didn't return cities, use config
            if not target_markets:
                target_markets = []
                for tier_name, cities in self.config.get('target_markets', {}).items():
                    target_markets.extend(cities)
            
            self.logger.info(f"Processing {len(target_markets)} markets")
            
            # Process markets with controlled concurrency
            max_concurrent = self.config['system']['max_concurrent_searches']
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_with_semaphore(market):
                async with semaphore:
                    return await self.process_market(market)
            
            # Process markets in batches
            tasks = []
            for market in target_markets:
                task = process_with_semaphore(market)
                tasks.append(task)
            
            # Execute with rate limiting
            batch_size = 5
            for i in range(0, len(tasks), batch_size):
                batch = tasks[i:i + batch_size]
                batch_results = await asyncio.gather(*batch, return_exceptions=True)
                
                for result in batch_results:
                    if isinstance(result, list):
                        all_leads.extend(result)
                    elif isinstance(result, Exception):
                        self.logger.error(f"Market processing error: {result}")
                
                # Rate limiting delay
                if i + batch_size < len(tasks):
                    await asyncio.sleep(self.config['system']['api_rate_limit_delay_seconds'])
            
            self.daily_stats['markets_processed'] += len(target_markets)
            
            # Send alerts for high-priority leads
            high_priority_leads = [lead for lead in all_leads if lead.priority in ['HIGH', 'INSTANT']]
            if high_priority_leads:
                await self.send_alert_email(high_priority_leads)
            
            # Update daily statistics
            await self.update_daily_statistics(int(time.time() - start_time))
            
            cycle_time = time.time() - start_time
            self.logger.info(f"Monitoring cycle completed: {len(all_leads)} leads found in {cycle_time:.1f}s")
            
        except Exception as e:
            self.logger.error(f"Error in monitoring cycle: {e}")
            self.logger.error(traceback.format_exc())
    
    async def update_daily_statistics(self, execution_time_seconds: int):
        """Update daily statistics in database"""
        try:
            today = datetime.now().date()
            
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO daily_statistics (
                        date, properties_analyzed, high_priority_found, alerts_sent, 
                        api_calls_made, markets_processed, execution_time_seconds
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                    ON CONFLICT (date) DO UPDATE SET
                        properties_analyzed = daily_statistics.properties_analyzed + EXCLUDED.properties_analyzed,
                        high_priority_found = daily_statistics.high_priority_found + EXCLUDED.high_priority_found,
                        alerts_sent = daily_statistics.alerts_sent + EXCLUDED.alerts_sent,
                        api_calls_made = daily_statistics.api_calls_made + EXCLUDED.api_calls_made,
                        markets_processed = daily_statistics.markets_processed + EXCLUDED.markets_processed,
                        execution_time_seconds = EXCLUDED.execution_time_seconds
                """, 
                    today,
                    self.daily_stats['properties_analyzed'],
                    self.daily_stats['high_priority_found'],
                    self.daily_stats['alerts_sent'],
                    self.daily_stats['api_calls_made'],
                    self.daily_stats['markets_processed'],
                    execution_time_seconds
                )
                
        except Exception as e:
            self.logger.error(f"Error updating daily statistics: {e}")
    
    async def generate_daily_report(self):
        """Generate daily performance report"""
        try:
            today = datetime.now().date()
            
            async with self.db_pool.acquire() as conn:
                # Get today's statistics
                stats = await conn.fetchrow("""
                    SELECT * FROM daily_statistics WHERE date = $1
                """, today)
                
                # Get top properties found today
                top_properties = await conn.fetch("""
                    SELECT address, city, state, score, priority, roi_percentage, cash_flow
                    FROM property_leads 
                    WHERE DATE(discovered_at) = $1 
                    ORDER BY score DESC 
                    LIMIT 10
                """, today)
                
                # Get market performance
                market_performance = await conn.fetch("""
                    SELECT city, state, COUNT(*) as properties_found, 
                           COUNT(*) FILTER (WHERE score >= 80) as high_priority
                    FROM property_leads 
                    WHERE DATE(discovered_at) = $1 
                    GROUP BY city, state 
                    ORDER BY properties_found DESC 
                    LIMIT 10
                """, today)
            
            # Create report
            report = {
                'date': today.isoformat(),
                'statistics': dict(stats) if stats else {},
                'top_properties': [dict(prop) for prop in top_properties],
                'market_performance': [dict(market) for market in market_performance],
                'generated_at': datetime.now().isoformat()
            }
            
            # Save report to file
            report_dir = Path('reports')
            report_dir.mkdir(exist_ok=True)
            
            report_file = report_dir / f"daily_report_{today.isoformat()}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Daily report generated: {report_file}")
            
        except Exception as e:
            self.logger.error(f"Error generating daily report: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.close()
            
            if self.db_pool:
                await self.db_pool.close()
                
            self.logger.info("Cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    async def run_forever(self):
        """Run the monitoring system continuously"""
        self.running = True
        
        try:
            await self.initialize()
            
            self.logger.info("Section 8 Monitor Pro started - Running continuous monitoring")
            
            # Schedule daily report generation
            schedule.every().day.at("18:00").do(lambda: asyncio.create_task(self.generate_daily_report()))
            
            while self.running:
                try:
                    # Run monitoring cycle
                    await self.run_monitoring_cycle()
                    
                    # Check for scheduled tasks
                    schedule.run_pending()
                    
                    # Reset daily stats at midnight
                    if datetime.now().hour == 0 and datetime.now().minute < 10:
                        self.daily_stats = {key: 0 for key in self.daily_stats}
                    
                    # Wait for next cycle
                    await asyncio.sleep(self.config['system']['search_interval_minutes'] * 60)
                    
                except KeyboardInterrupt:
                    self.logger.info("Shutdown requested by user")
                    break
                except Exception as e:
                    self.logger.error(f"Error in main loop: {e}")
                    await asyncio.sleep(60)  # Wait before retrying
                    
        finally:
            self.running = False
            await self.cleanup()

async def main():
    """Main entry point"""
    monitor = Section8MonitorPro()
    
    try:
        await monitor.run_forever()
    except KeyboardInterrupt:
        print("\nShutdown requested...")
    except Exception as e:
        print(f"Fatal error: {e}")
        traceback.print_exc()
    finally:
        await monitor.cleanup()

if __name__ == "__main__":
    # Set up event loop policy for Windows compatibility
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())
