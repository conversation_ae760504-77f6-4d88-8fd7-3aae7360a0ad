#!/usr/bin/env python3
"""
Section8 Investor Pro - Advanced Nationwide Property Monitor
Comprehensive nationwide Section 8 property monitoring system with HUD integration
"""

import os
import asyncio
import aiohttp
import asyncpg
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import schedule
import traceback

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

@dataclass
class MarketConfig:
    city: str
    state: str
    metro: str
    priority: str
    weight: float
    zip_codes: Optional[List[str]] = None
    median_income: Optional[int] = None
    population: Optional[int] = None

@dataclass
class PropertyAnalysis:
    property_id: str
    address: str
    city: str
    state: str
    zip_code: str
    price: int
    bedrooms: int
    bathrooms: float
    square_feet: int
    year_built: int
    estimated_rent: int
    hud_fmr: int
    cash_flow: int
    roi_percentage: float
    investment_score: int
    priority_level: str
    analysis_reasons: List[str]
    market_data: Dict
    discovered_at: datetime

class Section8InvestorPro:
    def __init__(self):
        self.setup_logging()
        self.load_configuration()
        self.setup_database_pool()
        self.setup_api_clients()
        self.property_cache = {}
        self.daily_stats = {
            'properties_analyzed': 0,
            'high_priority_found': 0,
            'api_calls_made': 0,
            'emails_sent': 0
        }
        
    def setup_logging(self):
        """Configure comprehensive logging"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('section8_monitor_pro.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_configuration(self):
        """Load system configuration from environment and config files"""
        self.config = {
            'apis': {
                'real_estate_key': os.getenv('REAL_ESTATE_API_KEY'),
                'real_estate_url': os.getenv('REAL_ESTATE_API_URL', 'https://api.realestateapi.com'),
                'hud_key': os.getenv('HUD_API_KEY'),
                'hud_url': os.getenv('HUD_API_URL', 'https://www.huduser.gov/hudapi/public'),
                'resend_key': os.getenv('RESEND_API_KEY'),
                'mapbox_token': os.getenv('MAPBOX_ACCESS_TOKEN'),
                'census_key': os.getenv('CENSUS_API_KEY')
            },
            'database': {
                'url': os.getenv('DATABASE_URL'),
                'pool_size': int(os.getenv('DATABASE_POOL_SIZE', '20'))
            },
            'system': {
                'search_interval': int(os.getenv('SEARCH_INTERVAL_MINUTES', '5')),
                'max_concurrent': int(os.getenv('MAX_CONCURRENT_SEARCHES', '10')),
                'rate_limit_delay': int(os.getenv('API_RATE_LIMIT_DELAY', '2')),
                'max_properties_per_search': int(os.getenv('MAX_PROPERTIES_PER_SEARCH', '100')),
                'max_daily_api_calls': int(os.getenv('MAX_DAILY_API_CALLS', '10000'))
            },
            'email': {
                'alert_email': os.getenv('ALERT_EMAIL'),
                'app_name': os.getenv('APP_NAME', 'Section8 Investor Pro')
            }
        }
        
        # Load market configuration
        self.load_market_configuration()
        
    def load_market_configuration(self):
        """Load comprehensive market configuration"""
        self.markets = {
            'tier_1_rust_belt': [
                MarketConfig('DETROIT', 'MI', 'Detroit-Warren-Dearborn', 'VERY_HIGH', 1.0, 
                           ['48201', '48202', '48204', '48206', '48208', '48210', '48212']),
                MarketConfig('CLEVELAND', 'OH', 'Cleveland-Elyria', 'VERY_HIGH', 1.0,
                           ['44102', '44103', '44104', '44105', '44108', '44109', '44110']),
                MarketConfig('PITTSBURGH', 'PA', 'Pittsburgh', 'VERY_HIGH', 1.0,
                           ['15201', '15202', '15203', '15204', '15205', '15206', '15207']),
                MarketConfig('BUFFALO', 'NY', 'Buffalo-Cheektowaga-Niagara Falls', 'VERY_HIGH', 1.0,
                           ['14201', '14202', '14203', '14204', '14206', '14207', '14208']),
                MarketConfig('MILWAUKEE', 'WI', 'Milwaukee-Waukesha-West Allis', 'VERY_HIGH', 1.0,
                           ['53202', '53203', '53204', '53205', '53206', '53208', '53210'])
            ],
            
            'tier_2_midwest': [
                MarketConfig('AKRON', 'OH', 'Akron', 'HIGH', 0.9,
                           ['44301', '44302', '44303', '44304', '44305', '44306', '44307']),
                MarketConfig('TOLEDO', 'OH', 'Toledo', 'HIGH', 0.9,
                           ['43602', '43604', '43605', '43606', '43607', '43608', '43609']),
                MarketConfig('DAYTON', 'OH', 'Dayton', 'HIGH', 0.9,
                           ['45401', '45402', '45403', '45404', '45405', '45406', '45407']),
                MarketConfig('FLINT', 'MI', 'Flint', 'HIGH', 0.9,
                           ['48501', '48502', '48503', '48504', '48505', '48506', '48507']),
                MarketConfig('GARY', 'IN', 'Chicago-Naperville-Elgin', 'HIGH', 0.9,
                           ['46401', '46402', '46403', '46404', '46405', '46406', '46407'])
            ],
            
            'tier_3_south': [
                MarketConfig('BIRMINGHAM', 'AL', 'Birmingham-Hoover', 'HIGH', 0.8,
                           ['35201', '35202', '35203', '35204', '35205', '35206', '35207']),
                MarketConfig('MEMPHIS', 'TN', 'Memphis', 'HIGH', 0.8,
                           ['38101', '38103', '38104', '38105', '38106', '38107', '38108']),
                MarketConfig('JACKSON', 'MS', 'Jackson', 'HIGH', 0.8,
                           ['39201', '39202', '39203', '39204', '39205', '39206', '39207']),
                MarketConfig('LITTLE ROCK', 'AR', 'Little Rock-North Little Rock-Conway', 'HIGH', 0.8,
                           ['72201', '72202', '72203', '72204', '72205', '72206', '72207']),
                MarketConfig('SHREVEPORT', 'LA', 'Shreveport-Bossier City', 'HIGH', 0.8,
                           ['71101', '71102', '71103', '71104', '71105', '71106', '71107'])
            ],
            
            'tier_4_opportunity': [
                MarketConfig('YOUNGSTOWN', 'OH', 'Youngstown-Warren-Boardman', 'MEDIUM', 0.7,
                           ['44503', '44504', '44505', '44506', '44507', '44508', '44509']),
                MarketConfig('CAMDEN', 'NJ', 'Philadelphia-Camden-Wilmington', 'MEDIUM', 0.7,
                           ['08101', '08102', '08103', '08104', '08105', '08106', '08107']),
                MarketConfig('TRENTON', 'NJ', 'Trenton', 'MEDIUM', 0.7,
                           ['08601', '08602', '08603', '08604', '08605', '08606', '08607']),
                MarketConfig('READING', 'PA', 'Reading', 'MEDIUM', 0.7,
                           ['19601', '19602', '19603', '19604', '19605', '19606', '19607']),
                MarketConfig('HARRISBURG', 'PA', 'Harrisburg-Carlisle', 'MEDIUM', 0.7,
                           ['17101', '17102', '17103', '17104', '17105', '17106', '17107'])
            ]
        }
        
        # Investment criteria configuration
        self.investment_criteria = {
            'price_ranges': {
                'tier_1': {'min': 20000, 'max': 100000},   # Rust Belt bargains
                'tier_2': {'min': 50000, 'max': 150000},   # Mid-range opportunities  
                'tier_3': {'min': 75000, 'max': 200000},   # Higher quality properties
                'tier_4': {'min': 100000, 'max': 250000}   # Premium Section 8 properties
            },
            'property_specs': {
                'bedrooms_min': 2,
                'bedrooms_max': 6,
                'bathrooms_min': 1,
                'square_feet_min': 700,
                'square_feet_max': 3000,
                'year_built_min': 1950,
                'lot_size_min': 0
            },
            'investment_filters': {
                'min_roi_percentage': 8.0,
                'preferred_roi_percentage': 15.0,
                'max_price_to_rent_ratio': 120,
                'min_cap_rate': 0.08,
                'max_vacancy_rate': 0.15
            },
            'scoring_weights': {
                'roi_weight': 30,
                'price_range_weight': 25,
                'location_weight': 20,
                'property_condition_weight': 15,
                'market_strength_weight': 10
            }
        }
        
    async def setup_database_pool(self):
        """Setup PostgreSQL connection pool"""
        try:
            self.db_pool = await asyncpg.create_pool(
                self.config['database']['url'],
                min_size=5,
                max_size=self.config['database']['pool_size'],
                command_timeout=60
            )
            
            # Initialize database schema
            await self.initialize_database_schema()
            self.logger.info("Database connection pool established")
            
        except Exception as e:
            self.logger.error(f"Database setup failed: {str(e)}")
            raise
    
    async def initialize_database_schema(self):
        """Create database tables if they don't exist"""
        schema_sql = """
        -- Properties table
        CREATE TABLE IF NOT EXISTS properties (
            id SERIAL PRIMARY KEY,
            property_id VARCHAR(100) UNIQUE NOT NULL,
            address TEXT NOT NULL,
            city VARCHAR(100) NOT NULL,
            state VARCHAR(2) NOT NULL,
            zip_code VARCHAR(10) NOT NULL,
            price INTEGER NOT NULL,
            bedrooms INTEGER,
            bathrooms DECIMAL(3,1),
            square_feet INTEGER,
            lot_square_feet INTEGER,
            year_built INTEGER,
            property_type VARCHAR(50),
            estimated_rent INTEGER,
            hud_fmr INTEGER,
            cash_flow INTEGER,
            roi_percentage DECIMAL(5,2),
            investment_score INTEGER,
            priority_level VARCHAR(20),
            analysis_reasons JSONB,
            market_data JSONB,
            discovered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            status VARCHAR(20) DEFAULT 'NEW'
        );
        
        -- Markets table
        CREATE TABLE IF NOT EXISTS markets (
            id SERIAL PRIMARY KEY,
            city VARCHAR(100) NOT NULL,
            state VARCHAR(2) NOT NULL,
            metro VARCHAR(200),
            priority VARCHAR(20),
            weight DECIMAL(3,2),
            median_income INTEGER,
            population INTEGER,
            avg_fmr JSONB,
            market_stats JSONB,
            last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(city, state)
        );
        
        -- Search history table
        CREATE TABLE IF NOT EXISTS search_history (
            id SERIAL PRIMARY KEY,
            market_city VARCHAR(100),
            market_state VARCHAR(2),
            search_type VARCHAR(50),
            properties_found INTEGER,
            high_priority_count INTEGER,
            api_calls_used INTEGER,
            search_duration INTEGER,
            search_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Daily statistics table
        CREATE TABLE IF NOT EXISTS daily_stats (
            date DATE PRIMARY KEY,
            total_properties_analyzed INTEGER DEFAULT 0,
            high_priority_found INTEGER DEFAULT 0,
            medium_priority_found INTEGER DEFAULT 0,
            total_api_calls INTEGER DEFAULT 0,
            emails_sent INTEGER DEFAULT 0,
            markets_searched INTEGER DEFAULT 0,
            avg_investment_score DECIMAL(5,2),
            top_market VARCHAR(100)
        );
        
        -- HUD Fair Market Rent cache table
        CREATE TABLE IF NOT EXISTS hud_fmr_cache (
            id SERIAL PRIMARY KEY,
            zip_code VARCHAR(10) NOT NULL,
            county VARCHAR(100),
            state VARCHAR(2) NOT NULL,
            metro_code VARCHAR(10),
            year INTEGER NOT NULL,
            efficiency INTEGER,
            one_bedroom INTEGER,
            two_bedroom INTEGER,
            three_bedroom INTEGER,
            four_bedroom INTEGER,
            cached_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(zip_code, year)
        );
        
        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_properties_city_state ON properties(city, state);
        CREATE INDEX IF NOT EXISTS idx_properties_priority ON properties(priority_level);
        CREATE INDEX IF NOT EXISTS idx_properties_discovered_at ON properties(discovered_at);
        CREATE INDEX IF NOT EXISTS idx_properties_roi ON properties(roi_percentage);
        CREATE INDEX IF NOT EXISTS idx_hud_fmr_zip_year ON hud_fmr_cache(zip_code, year);
        """
        
        async with self.db_pool.acquire() as conn:
            await conn.execute(schema_sql)
            
    def setup_api_clients(self):
        """Setup API client configurations"""
        self.api_headers = {
            'real_estate': {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {self.config['apis']['real_estate_key']}"
            },
            'hud': {
                'Authorization': f"Bearer {self.config['apis']['hud_key']}"
            },
            'resend': {
                'Authorization': f"Bearer {self.config['apis']['resend_key']}",
                'Content-Type': 'application/json'
            }
        }
        
    async def get_hud_fair_market_rent(self, zip_code: str, year: int = None) -> Dict:
        """Get HUD Fair Market Rent data for a ZIP code"""
        if year is None:
            year = datetime.now().year
            
        # Check cache first
        async with self.db_pool.acquire() as conn:
            cached_fmr = await conn.fetchrow(
                "SELECT * FROM hud_fmr_cache WHERE zip_code = $1 AND year = $2",
                zip_code, year
            )
            
            if cached_fmr and (datetime.now() - cached_fmr['cached_at']).days < 30:
                return {
                    'efficiency': cached_fmr['efficiency'],
                    'one_bedroom': cached_fmr['one_bedroom'], 
                    'two_bedroom': cached_fmr['two_bedroom'],
                    'three_bedroom': cached_fmr['three_bedroom'],
                    'four_bedroom': cached_fmr['four_bedroom']
                }
        
        # Fetch from HUD API
        try:
            url = f"{self.config['apis']['hud_url']}/fmr/zip/{zip_code}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.api_headers['hud']) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'data' in data and data['data']:
                            fmr_data = data['data']['basicdata']
                            
                            # Cache the data
                            async with self.db_pool.acquire() as conn:
                                await conn.execute("""
                                    INSERT INTO hud_fmr_cache 
                                    (zip_code, county, state, metro_code, year, efficiency, 
                                     one_bedroom, two_bedroom, three_bedroom, four_bedroom)
                                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                                    ON CONFLICT (zip_code, year) DO UPDATE SET
                                    efficiency = $6, one_bedroom = $7, two_bedroom = $8,
                                    three_bedroom = $9, four_bedroom = $10, cached_at = NOW()
                                """, 
                                    zip_code, fmr_data.get('county_name'),
                                    fmr_data.get('state'), fmr_data.get('metro_code'),
                                    year, fmr_data.get('Efficiency'),
                                    fmr_data.get('One-Bedroom'), fmr_data.get('Two-Bedroom'),
                                    fmr_data.get('Three-Bedroom'), fmr_data.get('Four-Bedroom')
                                )
                            
                            return {
                                'efficiency': fmr_data.get('Efficiency', 0),
                                'one_bedroom': fmr_data.get('One-Bedroom', 0),
                                'two_bedroom': fmr_data.get('Two-Bedroom', 0),
                                'three_bedroom': fmr_data.get('Three-Bedroom', 0),
                                'four_bedroom': fmr_data.get('Four-Bedroom', 0)
                            }
                            
        except Exception as e:
            self.logger.error(f"HUD API error for ZIP {zip_code}: {str(e)}")
            
        # Return default estimates if HUD API fails
        return self.estimate_fair_market_rent(zip_code)
        
    def estimate_fair_market_rent(self, zip_code: str) -> Dict:
        """Estimate FMR when HUD API is unavailable"""
        # Basic estimates based on ZIP code patterns and national averages
        base_rent = 600
        
        # Adjust based on region (first digit of ZIP)
        first_digit = zip_code[0] if zip_code else '4'
        multipliers = {
            '0': 1.4,  # Northeast
            '1': 1.3,  # Northeast  
            '2': 1.1,  # Mid-Atlantic
            '3': 0.9,  # Southeast
            '4': 0.8,  # Midwest/South
            '5': 0.9,  # Central
            '6': 0.8,  # South Central
            '7': 0.9,  # Mountain
            '8': 1.2,  # Mountain/West
            '9': 1.5   # West Coast
        }
        
        multiplier = multipliers.get(first_digit, 1.0)
        
        return {
            'efficiency': int(base_rent * 0.7 * multiplier),
            'one_bedroom': int(base_rent * 0.85 * multiplier),
            'two_bedroom': int(base_rent * multiplier),
            'three_bedroom': int(base_rent * 1.3 * multiplier),
            'four_bedroom': int(base_rent * 1.6 * multiplier)
        }
        
    async def search_properties_in_market(self, market: MarketConfig, price_tier: str) -> List[Dict]:
        """Search for properties in a specific market"""
        price_range = self.investment_criteria['price_ranges'][price_tier]
        property_specs = self.investment_criteria['property_specs']
        
        search_params = {
            'city': market.city,
            'state': market.state,
            'estimated_value_min': price_range['min'],
            'estimated_value_max': price_range['max'],
            'bedrooms_min': property_specs['bedrooms_min'],
            'bedrooms_max': property_specs['bedrooms_max'],
            'bathrooms_min': property_specs['bathrooms_min'],
            'square_feet_min': property_specs['square_feet_min'],
            'square_feet_max': property_specs['square_feet_max'],
            'year_built_min': property_specs['year_built_min'],
            'property_type': ['SINGLE_FAMILY', 'MULTI_FAMILY', 'TOWNHOUSE', 'CONDO'],
            'absentee_owner': True,
            'limit': self.config['system']['max_properties_per_search'],
            'sort': 'estimated_value',
            'sort_direction': 'asc'
        }
        
        try:
            url = f"{self.config['apis']['real_estate_url']}/v2/PropertySearch"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=self.api_headers['real_estate'], 
                                      json=search_params) as response:
                    
                    self.daily_stats['api_calls_made'] += 1
                    
                    if response.status == 200:
                        data = await response.json()
                        properties = data.get('data', [])
                        
                        self.logger.info(f"Found {len(properties)} properties in {market.city}, {market.state}")
                        return properties
                    else:
                        self.logger.error(f"API Error {response.status} for {market.city}: {await response.text()}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"Search error for {market.city}: {str(e)}")
            return []
            
    async def analyze_property_investment_potential(self, property_data: Dict, market: MarketConfig) -> PropertyAnalysis:
        """Comprehensive property investment analysis"""
        property_id = str(property_data.get('id', ''))
        address = property_data.get('address', '')
        city = property_data.get('city', '')
        state = property_data.get('state', '')
        zip_code = property_data.get('zip', '')
        price = property_data.get('estimatedValue', 0)
        bedrooms = property_data.get('bedrooms', 0)
        bathrooms = property_data.get('bathrooms', 0)
        square_feet = property_data.get('squareFeet', 0)
        year_built = property_data.get('yearBuilt', 0)
        
        # Get HUD Fair Market Rent
        hud_fmr_data = await self.get_hud_fair_market_rent(zip_code)
        
        # Determine appropriate FMR based on bedrooms
        hud_fmr = 0
        if bedrooms == 0:
            hud_fmr = hud_fmr_data.get('efficiency', 0)
        elif bedrooms == 1:
            hud_fmr = hud_fmr_data.get('one_bedroom', 0)
        elif bedrooms == 2:
            hud_fmr = hud_fmr_data.get('two_bedroom', 0)
        elif bedrooms == 3:
            hud_fmr = hud_fmr_data.get('three_bedroom', 0)
        elif bedrooms >= 4:
            hud_fmr = hud_fmr_data.get('four_bedroom', 0)
            
        # Estimate market rent (usually 90-95% of HUD FMR)
        estimated_rent = int(hud_fmr * 0.92) if hud_fmr > 0 else self.estimate_market_rent(square_feet, bedrooms, city, state)
        
        # Calculate investment metrics
        monthly_expenses = self.calculate_monthly_expenses(price, property_data)
        cash_flow = estimated_rent - monthly_expenses
        roi_percentage = (cash_flow * 12 / price * 100) if price > 0 else 0
        
        # Investment scoring
        score, reasons = self.calculate_investment_score(
            property_data, market, estimated_rent, cash_flow, roi_percentage
        )
        
        # Determine priority level
        priority_level = self.determine_priority_level(score, roi_percentage, cash_flow)
        
        return PropertyAnalysis(
            property_id=property_id,
            address=address,
            city=city,
            state=state,
            zip_code=zip_code,
            price=price,
            bedrooms=bedrooms,
            bathrooms=bathrooms,
            square_feet=square_feet,
            year_built=year_built,
            estimated_rent=estimated_rent,
            hud_fmr=hud_fmr,
            cash_flow=cash_flow,
            roi_percentage=roi_percentage,
            investment_score=score,
            priority_level=priority_level,
            analysis_reasons=reasons,
            market_data={
                'market_tier': market.priority,
                'market_weight': market.weight,
                'metro': market.metro
            },
            discovered_at=datetime.now()
        )
        
    def calculate_monthly_expenses(self, price: int, property_data: Dict) -> int:
        """Calculate estimated monthly expenses for property"""
        # Property taxes (estimate 1.2% annually)
        monthly_taxes = (price * 0.012) / 12
        
        # Insurance (estimate 0.6% annually)  
        monthly_insurance = (price * 0.006) / 12
        
        # Property management (8% of rent)
        estimated_rent = self.estimate_market_rent(
            property_data.get('squareFeet', 1000),
            property_data.get('bedrooms', 2),
            property_data.get('city', ''),
            property_data.get('state', '')
        )
        monthly_management = estimated_rent * 0.08
        
        # Maintenance reserve (5% of rent)
        monthly_maintenance = estimated_rent * 0.05
        
        # Vacancy reserve (8% of rent)
        monthly_vacancy = estimated_rent * 0.08
        
        return int(monthly_taxes + monthly_insurance + monthly_management + 
                  monthly_maintenance + monthly_vacancy)
                  
    def estimate_market_rent(self, square_feet: int, bedrooms: int, city: str, state: str) -> int:
        """Estimate market rent when HUD data unavailable"""
        # Base rent per square foot by region
        base_rates = {
            'MI': 0.65, 'OH': 0.60, 'PA': 0.70, 'NY': 0.80, 'WI': 0.65,
            'AL': 0.55, 'TN': 0.60, 'MS': 0.50, 'AR': 0.50, 'LA': 0.55,
            'IN': 0.60, 'NJ': 0.85, 'IL': 0.70
        }
        
        rate = base_rates.get(state, 0.60)
        base_rent = square_feet * rate
        
        # Adjust for bedroom count
        bedroom_multipliers = {1: 0.85, 2: 1.0, 3: 1.15, 4: 1.3, 5: 1.45, 6: 1.6}
        multiplier = bedroom_multipliers.get(bedrooms, 1.0)
        
        return int(base_rent * multiplier)
        
    def calculate_investment_score(self, property_data: Dict, market: MarketConfig, 
                                 estimated_rent: int, cash_flow: int, roi_percentage: float) -> Tuple[int, List[str]]:
        """Calculate comprehensive investment score"""
        score = 0
        reasons = []
        weights = self.investment_criteria['scoring_weights']
        
        # ROI Scoring (30 points max)
        if roi_percentage >= 20:
            score += 30
            reasons.append(f"Exceptional ROI: {roi_percentage:.1f}%")
        elif roi_percentage >= 15:
            score += 25
            reasons.append(f"Excellent ROI: {roi_percentage:.1f}%")
        elif roi_percentage >= 12:
            score += 20
            reasons.append(f"Strong ROI: {roi_percentage:.1f}%")
        elif roi_percentage >= 8:
            score += 15
            reasons.append(f"Good ROI: {roi_percentage:.1f}%")
        elif roi_percentage >= 5:
            score += 10
            reasons.append(f"Fair ROI: {roi_percentage:.1f}%")
            
        # Price Range Scoring (25 points max)
        price = property_data.get('estimatedValue', 0)
        if 20000 <= price <= 75000:
            score += 25
            reasons.append("Excellent price point for cash flow")
        elif 75000 <= price <= 125000:
            score += 20
            reasons.append("Good price range for Section 8")
        elif 125000 <= price <= 175000:
            score += 15
            reasons.append("Moderate price range")
        elif 175000 <= price <= 225000:
            score += 10
            reasons.append("Higher price range")
            
        # Location Scoring (20 points max)
        if market.priority == 'VERY_HIGH':
            score += 20
            reasons.append(f"Prime market: {market.metro}")
        elif market.priority == 'HIGH':
            score += 15
            reasons.append(f"Strong market: {market.metro}")
        elif market.priority == 'MEDIUM':
            score += 10
            reasons.append(f"Good market: {market.metro}")
            
        # Property Condition Scoring (15 points max)
        year_built = property_data.get('yearBuilt', 0)
        if year_built >= 2000:
            score += 15
            reasons.append("Modern construction")
        elif year_built >= 1990:
            score += 12
            reasons.append("Recent construction")
        elif year_built >= 1980:
            score += 10
            reasons.append("Good condition era")
        elif year_built >= 1970:
            score += 8
            reasons.append("Section 8 eligible")
        elif year_built >= 1950:
            score += 5
            reasons.append("Older but manageable")
            
        # Market Strength Scoring (10 points max)
        bedrooms = property_data.get('bedrooms', 0)
        if bedrooms == 3:
            score += 10
            reasons.append("3BR - highest Section 8 demand")
        elif bedrooms == 2:
            score += 8
            reasons.append("2BR - strong Section 8 demand")
        elif bedrooms == 4:
            score += 8
            reasons.append("4BR - family Section 8 demand")
        elif bedrooms >= 5:
            score += 6
            reasons.append("Large family Section 8 demand")
            
        # Bonus points for special conditions
        if property_data.get('absenteeOwner'):
            score += 5
            reasons.append("Absentee owner - motivated seller potential")
            
        if property_data.get('vacant'):
            score += 3
            reasons.append("Vacant - ready for immediate use")
            
        if cash_flow >= 300:
            score += 5
            reasons.append(f"Strong cash flow: ${cash_flow}/month")
        elif cash_flow >= 200:
            score += 3
            reasons.append(f"Good cash flow: ${cash_flow}/month")
            
        return min(score, 100), reasons
        
    def determine_priority_level(self, score: int, roi_percentage: float, cash_flow: int) -> str:
        """Determine investment priority level"""
        if score >= 75 and roi_percentage >= 15 and cash_flow >= 200:
            return 'VERY_HIGH'
        elif score >= 60 and roi_percentage >= 12 and cash_flow >= 150:
            return 'HIGH'
        elif score >= 45 and roi_percentage >= 8 and cash_flow >= 100:
            return 'MEDIUM'
        elif score >= 30 and roi_percentage >= 5:
            return 'LOW'
        else:
            return 'SKIP'
            
    async def save_property_analysis(self, analysis: PropertyAnalysis):
        """Save property analysis to database"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO properties 
                    (property_id, address, city, state, zip_code, price, bedrooms, bathrooms,
                     square_feet, year_built, estimated_rent, hud_fmr, cash_flow, roi_percentage,
                     investment_score, priority_level, analysis_reasons, market_data, discovered_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
                    ON CONFLICT (property_id) DO UPDATE SET
                    price = $6, estimated_rent = $11, hud_fmr = $12, cash_flow = $13,
                    roi_percentage = $14, investment_score = $15, priority_level = $16,
                    analysis_reasons = $17, market_data = $18, last_updated = NOW()
                """, 
                    analysis.property_id, analysis.address, analysis.city, analysis.state,
                    analysis.zip_code, analysis.price, analysis.bedrooms, analysis.bathrooms,
                    analysis.square_feet, analysis.year_built, analysis.estimated_rent,
                    analysis.hud_fmr, analysis.cash_flow, analysis.roi_percentage,
                    analysis.investment_score, analysis.priority_level,
                    json.dumps(analysis.analysis_reasons), json.dumps(analysis.market_data),
                    analysis.discovered_at
                )
                
        except Exception as e:
            self.logger.error(f"Database save error: {str(e)}")
            
    async def send_priority_alert(self, properties: List[PropertyAnalysis]):
        """Send email alert for high priority properties"""
        if not properties:
            return
            
        high_priority = [p for p in properties if p.priority_level in ['VERY_HIGH', 'HIGH']]
        
        if not high_priority:
            return
            
        subject = f"🏠 {len(high_priority)} High-Priority Section 8 Investment Opportunities!"
        
        # Create email body
        email_body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 800px; margin: 0 auto; padding: 20px;">
                <h1 style="color: #2c3e50; border-bottom: 3px solid #3498db;">
                    🏠 Section 8 Investment Alert
                </h1>
                
                <div style="background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3 style="margin: 0; color: #27ae60;">
                        {len(high_priority)} High-Priority Opportunities Found
                    </h3>
                    <p style="margin: 5px 0 0 0;">
                        Discovery Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                    </p>
                </div>
        """
        
        for i, prop in enumerate(high_priority[:10], 1):  # Show top 10
            priority_color = '#e74c3c' if prop.priority_level == 'VERY_HIGH' else '#f39c12'
            
            email_body += f"""
                <div style="border: 2px solid {priority_color}; border-radius: 8px; padding: 20px; margin: 20px 0; background: #fff;">
                    <h3 style="margin: 0 0 10px 0; color: {priority_color};">
                        {i}. {prop.address}
                    </h3>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0;">
                        <div>
                            <strong>💰 Investment Metrics:</strong><br>
                            Price: ${prop.price:,}<br>
                            Est. Rent: ${prop.estimated_rent:,}/month<br>
                            Cash Flow: ${prop.cash_flow:,}/month<br>
                            ROI: {prop.roi_percentage:.1f}%<br>
                            Investment Score: {prop.investment_score}/100
                        </div>
                        <div>
                            <strong>🏡 Property Details:</strong><br>
                            {prop.bedrooms}BR / {prop.bathrooms}BA<br>
                            {prop.square_feet:,} sq ft<br>
                            Built: {prop.year_built}<br>
                            HUD FMR: ${prop.hud_fmr:,}/month<br>
                            Priority: {prop.priority_level}
                        </div>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <strong>🎯 Why This Property:</strong>
                        <ul style="margin: 10px 0;">
            """
            
            for reason in prop.analysis_reasons[:5]:  # Top 5 reasons
                email_body += f"<li>{reason}</li>"
                
            email_body += """
                        </ul>
                    </div>
                </div>
            """
            
        email_body += f"""
                <div style="background: #3498db; color: white; padding: 20px; border-radius: 5px; margin: 30px 0;">
                    <h3 style="margin: 0 0 10px 0;">📊 Today's Summary</h3>
                    <p style="margin: 5px 0;">
                        Properties Analyzed: {self.daily_stats['properties_analyzed']}<br>
                        High Priority Found: {self.daily_stats['high_priority_found']}<br>
                        API Calls Used: {self.daily_stats['api_calls_made']}<br>
                        Markets Searched: Multiple nationwide markets
                    </p>
                </div>
                
                <div style="background: #2c3e50; color: white; padding: 15px; border-radius: 5px; text-align: center;">
                    <p style="margin: 0;">
                        <strong>Section8 Investor Pro</strong> - Advanced Property Monitoring System<br>
                        <small>Powered by HUD data and nationwide market analysis</small>
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Send email via Resend API
        payload = {
            "from": f"{self.config['email']['app_name']} <<EMAIL>>",
            "to": [self.config['email']['alert_email']],
            "subject": subject,
            "html": email_body
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post("https://api.resend.com/emails", 
                                      headers=self.api_headers['resend'], 
                                      json=payload) as response:
                    
                    if response.status == 200:
                        self.daily_stats['emails_sent'] += 1
                        self.logger.info(f"Alert email sent: {len(high_priority)} properties")
                    else:
                        self.logger.error(f"Email error {response.status}: {await response.text()}")
                        
        except Exception as e:
            self.logger.error(f"Email sending error: {str(e)}")
            
    async def run_market_search_cycle(self):
        """Run complete market search cycle"""
        self.logger.info("Starting comprehensive market search cycle...")
        
        start_time = time.time()
        all_analyses = []
        
        # Get all markets to search
        all_markets = []
        for tier_markets in self.markets.values():
            all_markets.extend(tier_markets)
        
        # Process markets concurrently
        semaphore = asyncio.Semaphore(self.config['system']['max_concurrent'])
        
        async def search_market_with_semaphore(market, price_tier):
            async with semaphore:
                try:
                    properties = await self.search_properties_in_market(market, price_tier)
                    
                    market_analyses = []
                    for prop in properties:
                        analysis = await self.analyze_property_investment_potential(prop, market)
                        
                        if analysis.priority_level != 'SKIP':
                            await self.save_property_analysis(analysis)
                            market_analyses.append(analysis)
                            
                            self.daily_stats['properties_analyzed'] += 1
                            if analysis.priority_level in ['VERY_HIGH', 'HIGH']:
                                self.daily_stats['high_priority_found'] += 1
                    
                    # Rate limiting
                    await asyncio.sleep(self.config['system']['rate_limit_delay'])
                    return market_analyses
                    
                except Exception as e:
                    self.logger.error(f"Error processing {market.city}: {str(e)}")
                    return []
        
        # Create tasks for all market/tier combinations
        tasks = []
        for market in all_markets:
            # Search different price tiers based on market priority
            if market.priority == 'VERY_HIGH':
                tiers = ['tier_1', 'tier_2']
            elif market.priority == 'HIGH':
                tiers = ['tier_2', 'tier_3']
            else:
                tiers = ['tier_3', 'tier_4']
                
            for tier in tiers:
                tasks.append(search_market_with_semaphore(market, tier))
        
        # Execute all searches
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Collect all analyses
        for result in results:
            if isinstance(result, list):
                all_analyses.extend(result)
                
        # Send alerts for high priority properties
        high_priority = [a for a in all_analyses if a.priority_level in ['VERY_HIGH', 'HIGH']]
        if high_priority:
            await self.send_priority_alert(high_priority)
            
        duration = time.time() - start_time
        self.logger.info(f"Search cycle completed: {len(all_analyses)} properties analyzed, "
                        f"{len(high_priority)} high priority, {duration:.1f}s")
        
    async def update_daily_statistics(self):
        """Update daily statistics in database"""
        today = datetime.now().date()
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO daily_stats 
                    (date, total_properties_analyzed, high_priority_found, total_api_calls, emails_sent)
                    VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (date) DO UPDATE SET
                    total_properties_analyzed = daily_stats.total_properties_analyzed + $2,
                    high_priority_found = daily_stats.high_priority_found + $3,
                    total_api_calls = daily_stats.total_api_calls + $4,
                    emails_sent = daily_stats.emails_sent + $5
                """, 
                    today, self.daily_stats['properties_analyzed'],
                    self.daily_stats['high_priority_found'], self.daily_stats['api_calls_made'],
                    self.daily_stats['emails_sent']
                )
                
        except Exception as e:
            self.logger.error(f"Statistics update error: {str(e)}")
            
    async def run_monitor(self):
        """Main monitoring loop"""
        self.logger.info("Section8 Investor Pro - Advanced Monitor Starting...")
        self.logger.info(f"Monitoring {sum(len(markets) for markets in self.markets.values())} markets")
        self.logger.info(f"Search interval: {self.config['system']['search_interval']} minutes")
        
        # Schedule periodic searches
        schedule.every(self.config['system']['search_interval']).minutes.do(
            lambda: asyncio.create_task(self.run_market_search_cycle())
        )
        
        # Schedule daily statistics update
        schedule.every(1).hours.do(
            lambda: asyncio.create_task(self.update_daily_statistics())
        )
        
        # Run initial search
        await self.run_market_search_cycle()
        
        # Keep running
        while True:
            try:
                schedule.run_pending()
                await asyncio.sleep(30)  # Check every 30 seconds
                
                # Reset daily stats at midnight
                if datetime.now().hour == 0 and datetime.now().minute < 1:
                    self.daily_stats = {
                        'properties_analyzed': 0,
                        'high_priority_found': 0, 
                        'api_calls_made': 0,
                        'emails_sent': 0
                    }
                    
            except KeyboardInterrupt:
                self.logger.info("Monitor stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Monitor error: {str(e)}")
                self.logger.error(traceback.format_exc())
                await asyncio.sleep(60)  # Wait before retrying

async def main():
    """Main function"""
    print("🏠 Section8 Investor Pro - Advanced Nationwide Monitor")
    print("="*60)
    print("🎯 Monitoring 25+ markets nationwide with HUD integration")
    print("💡 Advanced AI scoring based on $30M+ portfolio analysis")
    print("📧 Real-time alerts for high-priority opportunities")
    print("🗄️ PostgreSQL database with comprehensive tracking")
    print("="*60)
    
    try:
        monitor = Section8InvestorPro()
        await monitor.run_monitor()
    except Exception as e:
        logging.error(f"System startup error: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
