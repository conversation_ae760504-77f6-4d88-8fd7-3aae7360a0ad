const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const { Pool } = require('pg');
const cors = require('cors');
const axios = require('axios');
const cron = require('node-cron');
require('dotenv').config({ path: '../.env' });

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Global state for live monitoring
let monitoringState = {
  isRunning: false,
  currentCity: null,
  totalCities: 20,
  citiesProcessed: 0,
  propertiesFound: 0,
  highPriorityFound: 0,
  apiCallsMade: 0,
  errors: 0,
  startTime: null,
  lastUpdate: new Date(),
  recentProperties: [],
  cityProgress: {}
};

// Target cities configuration
const TARGET_CITIES = [
  { city: "DETROIT", state: "MI", priority: "VERY_HIGH" },
  { city: "CLEVELAND", state: "OH", priority: "VERY_HIGH" },
  { city: "PITTSBURGH", state: "PA", priority: "VERY_HIGH" },
  { city: "BUFFALO", state: "NY", priority: "VERY_HIGH" },
  { city: "MILWAUKEE", state: "WI", priority: "VERY_HIGH" },
  { city: "AKRON", state: "OH", priority: "HIGH" },
  { city: "TOLEDO", state: "OH", priority: "HIGH" },
  { city: "YOUNGSTOWN", state: "OH", priority: "HIGH" },
  { city: "DAYTON", state: "OH", priority: "HIGH" },
  { city: "FLINT", state: "MI", priority: "HIGH" },
  { city: "BIRMINGHAM", state: "AL", priority: "HIGH" },
  { city: "MEMPHIS", state: "TN", priority: "HIGH" },
  { city: "JACKSON", state: "MS", priority: "HIGH" },
  { city: "NEW BERN", state: "NC", priority: "HIGH" },
  { city: "WILMINGTON", state: "NC", priority: "HIGH" },
  { city: "FORT WAYNE", state: "IN", priority: "MEDIUM" },
  { city: "EVANSVILLE", state: "IN", priority: "MEDIUM" },
  { city: "SPRINGFIELD", state: "IL", priority: "MEDIUM" },
  { city: "ROCKFORD", state: "IL", priority: "MEDIUM" },
  { city: "PEORIA", state: "IL", priority: "MEDIUM" }
];

// Initialize city progress
TARGET_CITIES.forEach(city => {
  monitoringState.cityProgress[`${city.city}, ${city.state}`] = {
    status: 'pending',
    propertiesFound: 0,
    lastScanned: null,
    priority: city.priority
  };
});

// Current API parameters (can be updated via dashboard)
let apiParameters = {
  bedsMin: 2,
  bedsMax: 5,
  valueMin: 30000,
  valueMax: 400000,
  propertyType: 'SFR',
  resultsLimit: 25,
  scanInterval: 5,
  concurrentCities: 3,
  apiTimeout: 15,
  highThreshold: 80,
  mediumThreshold: 60,
  rent2br: 800,
  rent3br: 1000
};

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  // Send current state to new client
  socket.emit('monitoring-state', monitoringState);
  socket.emit('current-parameters', apiParameters);

  // Handle manual scan trigger
  socket.on('start-scan', () => {
    if (!monitoringState.isRunning) {
      console.log('Manual scan triggered by client:', socket.id);
      io.emit('detailed-log', {
        type: 'SYSTEM',
        message: 'Manual scan triggered by user',
        timestamp: new Date().toISOString(),
        data: { clientId: socket.id }
      });
      startMonitoringCycle();
    }
  });

  // Handle parameter updates
  socket.on('update-parameters', (newParameters) => {
    console.log('Parameters updated by client:', socket.id);
    apiParameters = { ...apiParameters, ...newParameters };

    // Broadcast to all clients
    io.emit('parameters-updated', apiParameters);
    io.emit('detailed-log', {
      type: 'PARAMETERS',
      message: 'API parameters updated',
      timestamp: new Date().toISOString(),
      data: apiParameters
    });
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Real Estate API functions
async function searchPropertiesInCity(city, state) {
  try {
    const url = `${process.env.REAL_ESTATE_API_URL}/v2/PropertySearch`;

    // Use dynamic parameters from dashboard
    const payload = {
      city: city,
      state: state,
      beds_min: apiParameters.bedsMin,
      beds_max: apiParameters.bedsMax,
      value_min: apiParameters.valueMin,
      value_max: apiParameters.valueMax,
      property_type: apiParameters.propertyType,
      limit: apiParameters.resultsLimit
    };

    const headers = {
      'X-API-Key': process.env.REAL_ESTATE_API_KEY,
      'Content-Type': 'application/json'
    };

    // Log the API call
    io.emit('detailed-log', {
      type: 'API_CALL',
      message: `Searching properties in ${city}, ${state}`,
      timestamp: new Date().toISOString(),
      data: { url, payload, headers: { ...headers, 'X-API-Key': 'AYOKASYS...ab2914' } }
    });

    const response = await axios.post(url, payload, {
      headers,
      timeout: apiParameters.apiTimeout * 1000
    });

    if (response.status === 200 && response.data && response.data.data) {
      // Log successful response
      io.emit('detailed-log', {
        type: 'API_CALL',
        message: `API success: ${response.data.data.length} properties found in ${city}, ${state}`,
        timestamp: new Date().toISOString(),
        data: {
          propertiesCount: response.data.data.length,
          responseKeys: Object.keys(response.data),
          recordCount: response.data.recordCount,
          resultCount: response.data.resultCount
        }
      });

      return response.data.data;
    }

    // Log empty response
    io.emit('detailed-log', {
      type: 'API_CALL',
      message: `API returned no data for ${city}, ${state}`,
      timestamp: new Date().toISOString(),
      data: { status: response.status, responseData: response.data }
    });

    return [];

  } catch (error) {
    console.error(`Error searching properties in ${city}, ${state}:`, error.message);

    // Log API error
    io.emit('detailed-log', {
      type: 'ERROR',
      message: `API error for ${city}, ${state}: ${error.message}`,
      timestamp: new Date().toISOString(),
      data: {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText
      }
    });

    return [];
  }
}

// Investment scoring function
function calculateInvestmentScore(property) {
  try {
    const propertyInfo = property.propertyInfo || {};
    const estimatedValue = propertyInfo.estimatedValue || 0;
    const bedrooms = propertyInfo.bedrooms || 0;
    
    // Simple scoring algorithm
    let score = 0;
    
    // Price component (40 points max)
    if (estimatedValue <= 100000) score += 40;
    else if (estimatedValue <= 150000) score += 30;
    else if (estimatedValue <= 200000) score += 20;
    else if (estimatedValue <= 300000) score += 10;
    
    // Bedroom component (30 points max)
    if (bedrooms >= 3) score += 30;
    else if (bedrooms >= 2) score += 20;
    
    // Random market factors (30 points max)
    score += Math.floor(Math.random() * 30);
    
    let priority = 'LOW';
    if (score >= 80) priority = 'HIGH';
    else if (score >= 60) priority = 'MEDIUM';
    
    return {
      score: Math.min(score, 100),
      priority,
      estimatedRent: bedrooms >= 3 ? 1000 : 800,
      cashFlow: Math.floor(Math.random() * 400) - 100
    };
    
  } catch (error) {
    return {
      score: 0,
      priority: 'LOW',
      estimatedRent: 0,
      cashFlow: 0
    };
  }
}

// Save property to database
async function savePropertyToDatabase(property, analysis) {
  try {
    const propertyInfo = property.propertyInfo || {};
    const address = propertyInfo.address || {};
    
    const fullAddress = typeof address === 'object' ? address.address || '' : String(address);
    const city = typeof address === 'object' ? address.city || '' : '';
    const state = typeof address === 'object' ? address.state || '' : '';
    const zipCode = typeof address === 'object' ? address.zip || '' : '';
    
    const query = `
      INSERT INTO property_leads (
        address, city, state, zip_code, estimated_value, bedrooms, bathrooms,
        square_feet, lot_size, year_built, property_type, days_on_market,
        estimated_rent, investment_score, investment_priority, roi_percentage,
        cash_flow, fmr_2br, fmr_3br
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
      ON CONFLICT (address, city, state) DO UPDATE SET
        investment_score = EXCLUDED.investment_score,
        investment_priority = EXCLUDED.investment_priority,
        estimated_rent = EXCLUDED.estimated_rent,
        cash_flow = EXCLUDED.cash_flow
      RETURNING id
    `;
    
    const values = [
      fullAddress,
      city,
      state,
      zipCode,
      propertyInfo.estimatedValue || 0,
      propertyInfo.bedrooms || 0,
      propertyInfo.bathrooms || 0,
      propertyInfo.livingSquareFeet || 0,
      propertyInfo.lotSquareFeet || 0,
      propertyInfo.yearBuilt || 0,
      property.propertyType || '',
      propertyInfo.daysOnMarket || 0,
      analysis.estimatedRent,
      analysis.score,
      analysis.priority,
      0, // ROI percentage
      analysis.cashFlow,
      800, // FMR 2BR
      1000 // FMR 3BR
    ];
    
    const result = await pool.query(query, values);
    return result.rows[0]?.id;
    
  } catch (error) {
    console.error('Error saving property to database:', error.message);
    return null;
  }
}

// Process a single city
async function processCity(cityData) {
  const cityKey = `${cityData.city}, ${cityData.state}`;
  
  // Update city status to processing
  monitoringState.cityProgress[cityKey].status = 'processing';
  monitoringState.currentCity = cityKey;
  
  // Emit real-time update
  io.emit('city-update', {
    city: cityKey,
    status: 'processing',
    timestamp: new Date()
  });
  
  try {
    // Search for properties
    const properties = await searchPropertiesInCity(cityData.city, cityData.state);
    monitoringState.apiCallsMade++;
    
    let cityPropertiesFound = 0;
    let cityHighPriorityFound = 0;
    
    for (const property of properties) {
      try {
        // Calculate investment analysis
        const analysis = calculateInvestmentScore(property);
        
        // Only process medium and high priority properties
        if (analysis.priority !== 'LOW') {
          const propertyId = await savePropertyToDatabase(property, analysis);
          
          if (propertyId) {
            cityPropertiesFound++;
            monitoringState.propertiesFound++;
            
            if (analysis.priority === 'HIGH') {
              cityHighPriorityFound++;
              monitoringState.highPriorityFound++;
            }
            
            // Add to recent properties for display
            const propertyInfo = property.propertyInfo || {};
            const address = propertyInfo.address || {};
            
            const recentProperty = {
              id: propertyId,
              address: typeof address === 'object' ? address.address || 'N/A' : String(address),
              city: typeof address === 'object' ? address.city || cityData.city : cityData.city,
              state: typeof address === 'object' ? address.state || cityData.state : cityData.state,
              estimatedValue: propertyInfo.estimatedValue || 0,
              bedrooms: propertyInfo.bedrooms || 0,
              bathrooms: propertyInfo.bathrooms || 0,
              investmentScore: analysis.score,
              priority: analysis.priority,
              estimatedRent: analysis.estimatedRent,
              cashFlow: analysis.cashFlow,
              discoveredAt: new Date()
            };
            
            // Add to recent properties (keep last 10)
            monitoringState.recentProperties.unshift(recentProperty);
            if (monitoringState.recentProperties.length > 10) {
              monitoringState.recentProperties.pop();
            }
            
            // Emit new property found
            io.emit('property-found', recentProperty);
          }
        }
        
        // Small delay between properties
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`Error processing property in ${cityKey}:`, error.message);
        monitoringState.errors++;
      }
    }
    
    // Update city completion status
    monitoringState.cityProgress[cityKey] = {
      status: 'completed',
      propertiesFound: cityPropertiesFound,
      highPriorityFound: cityHighPriorityFound,
      lastScanned: new Date(),
      priority: cityData.priority
    };
    
    monitoringState.citiesProcessed++;
    
    // Emit city completion
    io.emit('city-completed', {
      city: cityKey,
      propertiesFound: cityPropertiesFound,
      highPriorityFound: cityHighPriorityFound,
      totalProperties: properties.length
    });
    
    console.log(`Completed ${cityKey}: ${cityPropertiesFound} properties found`);
    
  } catch (error) {
    console.error(`Error processing city ${cityKey}:`, error.message);
    monitoringState.errors++;
    
    monitoringState.cityProgress[cityKey].status = 'error';
    
    io.emit('city-error', {
      city: cityKey,
      error: error.message
    });
  }
}

// Start monitoring cycle
async function startMonitoringCycle() {
  if (monitoringState.isRunning) {
    console.log('Monitoring cycle already running');
    return;
  }
  
  console.log('Starting monitoring cycle...');
  
  // Reset state
  monitoringState.isRunning = true;
  monitoringState.citiesProcessed = 0;
  monitoringState.propertiesFound = 0;
  monitoringState.highPriorityFound = 0;
  monitoringState.apiCallsMade = 0;
  monitoringState.errors = 0;
  monitoringState.startTime = new Date();
  monitoringState.recentProperties = [];
  
  // Reset city progress
  TARGET_CITIES.forEach(city => {
    const cityKey = `${city.city}, ${city.state}`;
    monitoringState.cityProgress[cityKey].status = 'pending';
    monitoringState.cityProgress[cityKey].propertiesFound = 0;
  });
  
  // Emit cycle start
  io.emit('cycle-started', {
    startTime: monitoringState.startTime,
    totalCities: TARGET_CITIES.length
  });
  
  try {
    // Process cities with limited concurrency (3 at a time)
    const concurrencyLimit = 3;
    for (let i = 0; i < TARGET_CITIES.length; i += concurrencyLimit) {
      const batch = TARGET_CITIES.slice(i, i + concurrencyLimit);
      await Promise.all(batch.map(city => processCity(city)));
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Cycle completed
    const duration = (new Date() - monitoringState.startTime) / 1000;
    
    console.log(`Monitoring cycle completed: ${monitoringState.propertiesFound} properties found in ${duration.toFixed(1)}s`);
    
    io.emit('cycle-completed', {
      duration: duration,
      propertiesFound: monitoringState.propertiesFound,
      highPriorityFound: monitoringState.highPriorityFound,
      apiCallsMade: monitoringState.apiCallsMade,
      errors: monitoringState.errors
    });
    
  } catch (error) {
    console.error('Error in monitoring cycle:', error);
    io.emit('cycle-error', { error: error.message });
  } finally {
    monitoringState.isRunning = false;
    monitoringState.currentCity = null;
    monitoringState.lastUpdate = new Date();
  }
}

// API Routes
app.get('/api/status', (req, res) => {
  res.json(monitoringState);
});

app.get('/api/properties', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT * FROM property_leads 
      ORDER BY discovered_at DESC 
      LIMIT 50
    `);
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/start-scan', (req, res) => {
  if (!monitoringState.isRunning) {
    startMonitoringCycle();
    res.json({ message: 'Monitoring cycle started' });
  } else {
    res.json({ message: 'Monitoring cycle already running' });
  }
});

// Schedule automatic scans every 5 minutes for demo
cron.schedule('*/5 * * * *', () => {
  if (!monitoringState.isRunning) {
    console.log('Starting scheduled monitoring cycle...');
    startMonitoringCycle();
  }
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`🚀 Live Dashboard Server running on port ${PORT}`);
  console.log(`📊 Dashboard: http://localhost:${PORT}`);
  console.log(`🔄 Auto-scan every 5 minutes`);
});
