<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Section 8 Property Monitor - Live Dashboard</title>
    <script src="/socket.io/socket.io.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .city-processing {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            background-size: 200% 100%;
            animation: gradient 2s ease infinite;
        }
        
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .priority-high { border-left: 4px solid #ef4444; }
        .priority-medium { border-left: 4px solid #f59e0b; }
        .priority-low { border-left: 4px solid #6b7280; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 py-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">
                        🏠 Section 8 Property Monitor
                    </h1>
                    <p class="text-gray-600 mt-1">Live Real-Time Property Discovery</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="status-indicator" class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                        <span class="text-sm text-gray-600">Connecting...</span>
                    </div>
                    <button id="parameters-btn" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-cog mr-2"></i>Parameters
                    </button>
                    <button id="logs-btn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-file-alt mr-2"></i>Logs
                    </button>
                    <button id="start-scan-btn" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-play mr-2"></i>Start Scan
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Cities Processed</p>
                        <p id="cities-processed" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-map-marker-alt text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Properties Found</p>
                        <p id="properties-found" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-home text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">High Priority</p>
                        <p id="high-priority" class="text-2xl font-bold text-red-600">0</p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-fire text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">API Calls</p>
                        <p id="api-calls" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exchange-alt text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Cycle Time</p>
                        <p id="cycle-time" class="text-2xl font-bold text-gray-900">--</p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- City Progress -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-900">City Monitoring Progress</h2>
                        <p class="text-sm text-gray-600">Real-time scanning status across all target markets</p>
                    </div>
                    <div class="p-6">
                        <div id="city-progress" class="space-y-3">
                            <!-- Cities will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Properties -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-900">Recent Discoveries</h2>
                        <p class="text-sm text-gray-600">Latest high-priority properties found</p>
                    </div>
                    <div class="p-6">
                        <div id="recent-properties" class="space-y-4">
                            <div class="text-center text-gray-500 py-8">
                                <i class="fas fa-search text-4xl mb-4"></i>
                                <p>Waiting for properties...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Log -->
        <div class="mt-8">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">Live Activity Log</h2>
                    <p class="text-sm text-gray-600">Real-time monitoring events and discoveries</p>
                </div>
                <div class="p-6">
                    <div id="activity-log" class="space-y-2 max-h-96 overflow-y-auto">
                        <!-- Activity items will be added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Parameters Modal -->
    <div id="parameters-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">API Parameters & Configuration</h2>
                    <button id="close-parameters" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Real Estate API Parameters -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Real Estate API Parameters</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">API URL</label>
                                    <input id="api-url" type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                                    <input id="api-key" type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" readonly>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Min Bedrooms</label>
                                        <input id="beds-min" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="2">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Max Bedrooms</label>
                                        <input id="beds-max" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="5">
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Min Value ($)</label>
                                        <input id="value-min" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="30000">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Max Value ($)</label>
                                        <input id="value-max" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="400000">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Property Type</label>
                                    <select id="property-type" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                        <option value="SFR">Single Family (SFR)</option>
                                        <option value="MFR">Multi Family (MFR)</option>
                                        <option value="CON">Condo (CON)</option>
                                        <option value="TWH">Townhouse (TWH)</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Results Limit</label>
                                    <input id="results-limit" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="25">
                                </div>
                            </div>
                        </div>

                        <!-- System Configuration -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">System Configuration</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Scan Interval (minutes)</label>
                                    <input id="scan-interval" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="5">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Concurrent Cities</label>
                                    <input id="concurrent-cities" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="3">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">API Timeout (seconds)</label>
                                    <input id="api-timeout" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="15">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Database URL</label>
                                    <input id="database-url" type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" readonly>
                                </div>
                            </div>

                            <!-- Investment Scoring -->
                            <h4 class="text-md font-medium text-gray-900 mt-6 mb-4">Investment Scoring</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">High Priority Threshold</label>
                                    <input id="high-threshold" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="80">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Medium Priority Threshold</label>
                                    <input id="medium-threshold" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="60">
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">2BR Rent ($)</label>
                                        <input id="rent-2br" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="800">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">3BR Rent ($)</label>
                                        <input id="rent-3br" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="1000">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current API Call Example -->
                    <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-2">Current API Call Example</h4>
                        <pre id="api-example" class="text-xs text-gray-700 overflow-x-auto"></pre>
                    </div>

                    <!-- Save Button -->
                    <div class="mt-6 flex justify-end space-x-4">
                        <button id="reset-parameters" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                            Reset to Defaults
                        </button>
                        <button id="save-parameters" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            Save Parameters
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Modal -->
    <div id="logs-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-screen overflow-hidden">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">System Logs & Debug Information</h2>
                    <div class="flex items-center space-x-4">
                        <button id="clear-logs" class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                            Clear Logs
                        </button>
                        <button id="close-logs" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                <div class="p-6 h-96 overflow-y-auto">
                    <div id="detailed-logs" class="space-y-2 font-mono text-sm">
                        <!-- Detailed logs will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
