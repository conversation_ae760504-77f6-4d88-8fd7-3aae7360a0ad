<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Section 8 Property Monitor - Live Dashboard</title>
    <script src="/socket.io/socket.io.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .city-processing {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            background-size: 200% 100%;
            animation: gradient 2s ease infinite;
        }
        
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .priority-high { border-left: 4px solid #ef4444; }
        .priority-medium { border-left: 4px solid #f59e0b; }
        .priority-low { border-left: 4px solid #6b7280; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 py-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">
                        🏠 Section 8 Property Monitor
                    </h1>
                    <p class="text-gray-600 mt-1">Live Real-Time Property Discovery</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="status-indicator" class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                        <span class="text-sm text-gray-600">Connecting...</span>
                    </div>
                    <button id="parameters-btn" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-cog mr-2"></i>Parameters
                    </button>
                    <button id="logs-btn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-file-alt mr-2"></i>Logs
                    </button>
                    <button id="start-scan-btn" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-play mr-2"></i>Start Scan
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- Navigation Tabs -->
        <div class="mb-8">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button id="tab-dashboard" class="tab-button active border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </button>
                <button id="tab-cities" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                    <i class="fas fa-map-marker-alt mr-2"></i>Cities & Markets
                </button>
                <button id="tab-properties" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                    <i class="fas fa-home mr-2"></i>All Properties
                </button>
                <button id="tab-api-features" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                    <i class="fas fa-cogs mr-2"></i>API Features
                </button>
            </nav>
        </div>

        <!-- Dashboard Tab -->
        <div id="content-dashboard" class="tab-content">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Cities Processed</p>
                        <p id="cities-processed" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-map-marker-alt text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Properties Found</p>
                        <p id="properties-found" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-home text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">High Priority</p>
                        <p id="high-priority" class="text-2xl font-bold text-red-600">0</p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-fire text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">API Calls</p>
                        <p id="api-calls" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exchange-alt text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Cycle Time</p>
                        <p id="cycle-time" class="text-2xl font-bold text-gray-900">--</p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- City Progress -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-900">City Monitoring Progress</h2>
                        <p class="text-sm text-gray-600">Real-time scanning status across all target markets</p>
                    </div>
                    <div class="p-6">
                        <div id="city-progress" class="space-y-3">
                            <!-- Cities will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Properties -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-900">Recent Discoveries</h2>
                        <p class="text-sm text-gray-600">Latest high-priority properties found</p>
                    </div>
                    <div class="p-6">
                        <div id="recent-properties" class="space-y-4">
                            <div class="text-center text-gray-500 py-8">
                                <i class="fas fa-search text-4xl mb-4"></i>
                                <p>Waiting for properties...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Log -->
        <div class="mt-8">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">Live Activity Log</h2>
                    <p class="text-sm text-gray-600">Real-time monitoring events and discoveries</p>
                </div>
                <div class="p-6">
                    <div id="activity-log" class="space-y-2 max-h-96 overflow-y-auto">
                        <!-- Activity items will be added here -->
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- Cities & Markets Tab -->
        <div id="content-cities" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Current Target Cities -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-900">Current Target Cities</h2>
                        <p class="text-sm text-gray-600">Cities currently being monitored for Section 8 opportunities</p>
                    </div>
                    <div class="p-6">
                        <div id="current-cities-list" class="space-y-3">
                            <!-- Current cities will be populated here -->
                        </div>
                        <div class="mt-6 flex justify-between">
                            <button id="select-all-cities" class="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                                Select All
                            </button>
                            <button id="clear-all-cities" class="px-4 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-700">
                                Clear All
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Add New Cities -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-900">Add New Cities</h2>
                        <p class="text-sm text-gray-600">Search and add cities to monitor</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Search Cities</label>
                                <div class="flex space-x-2">
                                    <input id="city-search" type="text" placeholder="Enter city name..."
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <select id="state-select" class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                                        <option value="">All States</option>
                                        <option value="AL">Alabama</option>
                                        <option value="AR">Arkansas</option>
                                        <option value="FL">Florida</option>
                                        <option value="GA">Georgia</option>
                                        <option value="IL">Illinois</option>
                                        <option value="IN">Indiana</option>
                                        <option value="KY">Kentucky</option>
                                        <option value="LA">Louisiana</option>
                                        <option value="MI">Michigan</option>
                                        <option value="MS">Mississippi</option>
                                        <option value="NC">North Carolina</option>
                                        <option value="OH">Ohio</option>
                                        <option value="PA">Pennsylvania</option>
                                        <option value="SC">South Carolina</option>
                                        <option value="TN">Tennessee</option>
                                        <option value="TX">Texas</option>
                                        <option value="WV">West Virginia</option>
                                    </select>
                                    <button id="search-cities" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>

                            <div id="city-search-results" class="space-y-2 max-h-64 overflow-y-auto">
                                <!-- Search results will appear here -->
                            </div>

                            <div class="border-t pt-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Quick Add Popular Cities</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    <button class="quick-add-city px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                            data-city="ATLANTA" data-state="GA">Atlanta, GA</button>
                                    <button class="quick-add-city px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                            data-city="CHARLOTTE" data-state="NC">Charlotte, NC</button>
                                    <button class="quick-add-city px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                            data-city="INDIANAPOLIS" data-state="IN">Indianapolis, IN</button>
                                    <button class="quick-add-city px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                            data-city="LOUISVILLE" data-state="KY">Louisville, KY</button>
                                    <button class="quick-add-city px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                            data-city="NASHVILLE" data-state="TN">Nashville, TN</button>
                                    <button class="quick-add-city px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                            data-city="RICHMOND" data-state="VA">Richmond, VA</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- All Properties Tab -->
        <div id="content-properties" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">All Properties</h2>
                            <p class="text-sm text-gray-600">Complete list of discovered investment opportunities</p>
                        </div>
                        <div class="flex space-x-4">
                            <button id="export-properties" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                                <i class="fas fa-download mr-2"></i>Export CSV
                            </button>
                            <button id="refresh-properties" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                                <i class="fas fa-sync mr-2"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="px-6 py-4 bg-gray-50 border-b">
                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">Priority</label>
                            <select id="filter-priority" class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                                <option value="">All</option>
                                <option value="HIGH">High</option>
                                <option value="MEDIUM">Medium</option>
                                <option value="LOW">Low</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">City</label>
                            <select id="filter-city" class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                                <option value="">All Cities</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">Min ROI (%)</label>
                            <input id="filter-min-roi" type="number" placeholder="0" class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">Max Price</label>
                            <input id="filter-max-price" type="number" placeholder="500000" class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">Min Cash Flow</label>
                            <input id="filter-min-cash-flow" type="number" placeholder="0" class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                        </div>
                        <div class="flex items-end">
                            <button id="apply-filters" class="w-full px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Properties Table -->
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100" data-sort="address">
                                        Address <i class="fas fa-sort ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100" data-sort="investment_score">
                                        Score <i class="fas fa-sort ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100" data-sort="investment_priority">
                                        Priority <i class="fas fa-sort ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100" data-sort="estimated_value">
                                        Value <i class="fas fa-sort ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100" data-sort="roi_percentage">
                                        ROI <i class="fas fa-sort ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100" data-sort="cash_flow">
                                        Cash Flow <i class="fas fa-sort ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100" data-sort="discovered_at">
                                        Discovered <i class="fas fa-sort ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="properties-table-body" class="bg-white divide-y divide-gray-200">
                                <!-- Properties will be populated here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6 flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing <span id="properties-showing-start">0</span> to <span id="properties-showing-end">0</span> of <span id="properties-total">0</span> properties
                        </div>
                        <div class="flex space-x-2">
                            <button id="properties-prev-page" class="px-3 py-1 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50">
                                Previous
                            </button>
                            <span id="properties-page-info" class="px-3 py-1 text-sm text-gray-700">Page 1 of 1</span>
                            <button id="properties-next-page" class="px-3 py-1 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50">
                                Next
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Features Tab -->
        <div id="content-api-features" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Real Estate API Features -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-900">Real Estate API Features</h2>
                        <p class="text-sm text-gray-600">Available endpoints and capabilities</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="border rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-search text-blue-600 mr-2"></i>Property Search
                                </h4>
                                <p class="text-sm text-gray-600 mb-3">Search properties by location, price, bedrooms, and more</p>
                                <button class="test-api-btn px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                                        data-endpoint="search">Test API</button>
                            </div>

                            <div class="border rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-info-circle text-green-600 mr-2"></i>Property Details
                                </h4>
                                <p class="text-sm text-gray-600 mb-3">Get comprehensive property information and history</p>
                                <button class="test-api-btn px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                                        data-endpoint="details">Test API</button>
                            </div>

                            <div class="border rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-chart-line text-purple-600 mr-2"></i>Property Comparables
                                </h4>
                                <p class="text-sm text-gray-600 mb-3">Find similar properties for valuation analysis</p>
                                <button class="test-api-btn px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200"
                                        data-endpoint="comps">Test API</button>
                            </div>

                            <div class="border rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-calculator text-orange-600 mr-2"></i>Automated Valuation (AVM)
                                </h4>
                                <p class="text-sm text-gray-600 mb-3">Get AI-powered property value estimates</p>
                                <button class="test-api-btn px-3 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200"
                                        data-endpoint="avm">Test API</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- HUD API Features -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-900">HUD API Features</h2>
                        <p class="text-sm text-gray-600">Section 8 and Fair Market Rent data</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="border rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-dollar-sign text-green-600 mr-2"></i>Fair Market Rents (FMR)
                                </h4>
                                <p class="text-sm text-gray-600 mb-3">Get HUD Fair Market Rent data by city/county</p>
                                <button class="test-hud-btn px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                                        data-endpoint="fmr">Test API</button>
                            </div>

                            <div class="border rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-users text-purple-600 mr-2"></i>Income Limits
                                </h4>
                                <p class="text-sm text-gray-600 mb-3">Get Section 8 income eligibility limits</p>
                                <button class="test-hud-btn px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200"
                                        data-endpoint="income">Test API</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Test Results -->
            <div class="mt-8 bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">API Test Results</h2>
                    <p class="text-sm text-gray-600">Live API responses and data preview</p>
                </div>
                <div class="p-6">
                    <div id="api-test-results" class="bg-gray-50 rounded-lg p-4 min-h-32">
                        <p class="text-gray-500 text-center">Click any "Test API" button above to see live results</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Parameters Modal -->
    <div id="parameters-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">API Parameters & Configuration</h2>
                    <button id="close-parameters" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Real Estate API Parameters -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Real Estate API Parameters</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">API URL</label>
                                    <input id="api-url" type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                                    <input id="api-key" type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" readonly>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Min Bedrooms</label>
                                        <input id="beds-min" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="2">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Max Bedrooms</label>
                                        <input id="beds-max" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="5">
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Min Value ($)</label>
                                        <input id="value-min" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="30000">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Max Value ($)</label>
                                        <input id="value-max" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="400000">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Property Type</label>
                                    <select id="property-type" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                                        <option value="SFR">Single Family (SFR)</option>
                                        <option value="MFR">Multi Family (MFR)</option>
                                        <option value="CON">Condo (CON)</option>
                                        <option value="TWH">Townhouse (TWH)</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Results Limit</label>
                                    <input id="results-limit" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="25">
                                </div>
                            </div>
                        </div>

                        <!-- System Configuration -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">System Configuration</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Scan Interval (minutes)</label>
                                    <input id="scan-interval" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="5">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Concurrent Cities</label>
                                    <input id="concurrent-cities" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="3">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">API Timeout (seconds)</label>
                                    <input id="api-timeout" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="15">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Database URL</label>
                                    <input id="database-url" type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" readonly>
                                </div>
                            </div>

                            <!-- Investment Scoring -->
                            <h4 class="text-md font-medium text-gray-900 mt-6 mb-4">Investment Scoring</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">High Priority Threshold</label>
                                    <input id="high-threshold" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="80">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Medium Priority Threshold</label>
                                    <input id="medium-threshold" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="60">
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">2BR Rent ($)</label>
                                        <input id="rent-2br" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="800">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">3BR Rent ($)</label>
                                        <input id="rent-3br" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" value="1000">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current API Call Example -->
                    <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-2">Current API Call Example</h4>
                        <pre id="api-example" class="text-xs text-gray-700 overflow-x-auto"></pre>
                    </div>

                    <!-- Save Button -->
                    <div class="mt-6 flex justify-end space-x-4">
                        <button id="reset-parameters" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                            Reset to Defaults
                        </button>
                        <button id="save-parameters" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            Save Parameters
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Modal -->
    <div id="logs-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-screen overflow-hidden">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">System Logs & Debug Information</h2>
                    <div class="flex items-center space-x-4">
                        <button id="clear-logs" class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                            Clear Logs
                        </button>
                        <button id="close-logs" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                <div class="p-6 h-96 overflow-y-auto">
                    <div id="detailed-logs" class="space-y-2 font-mono text-sm">
                        <!-- Detailed logs will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
