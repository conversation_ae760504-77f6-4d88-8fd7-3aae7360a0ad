version: '3.8'

services:
  section8-monitor:
    build: .
    container_name: section8_investor_pro
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REAL_ESTATE_API_KEY=${REAL_ESTATE_API_KEY}
      - HUD_API_KEY=${HUD_API_KEY}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - ALERT_EMAIL=${ALERT_EMAIL}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./reports:/app/reports
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    networks:
      - section8_network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:15-alpine
    container_name: section8_postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=section8_db
      - POSTGRES_USER=section8_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-section8_secure_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - section8_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U section8_user -d section8_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: section8_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - section8_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  nginx:
    image: nginx:alpine
    container_name: section8_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - section8-monitor
    networks:
      - section8_network

  # Optional: Monitoring and metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: section8_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - section8_network

  grafana:
    image: grafana/grafana:latest
    container_name: section8_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/var/lib/grafana/dashboards
      - ./grafana/provisioning:/etc/grafana/provisioning
    networks:
      - section8_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  section8_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
