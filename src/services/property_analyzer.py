#!/usr/bin/env python3
"""
Property Analyzer for Section 8 Investor Pro
Advanced property analysis and scoring based on successful investor patterns
"""

import logging
import math
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..api.real_estate_client import RealEstateAPIClient
from ..api.hud_client import HUDAPIClient
from ..core.config import Config, InvestmentCriteria

@dataclass
class PropertyAnalysis:
    """Results of property analysis"""
    property_id: str
    address: str
    investment_score: int
    investment_priority: str
    roi_percentage: float
    estimated_rent: float
    estimated_cash_flow: float
    fair_market_rent: float
    analysis_reasons: List[str]
    risk_factors: List[str]
    opportunity_factors: List[str]
    comparable_properties: List[Dict]
    market_analysis: Dict[str, Any]

class PropertyAnalyzer:
    """Analyzes properties for Section 8 investment potential"""
    
    def __init__(
        self, 
        real_estate_client: RealEstateAPIClient,
        hud_client: HUDAPIClient,
        config: Config
    ):
        """
        Initialize property analyzer
        
        Args:
            real_estate_client: Real Estate API client
            hud_client: HUD API client
            config: Application configuration
        """
        self.real_estate_client = real_estate_client
        self.hud_client = hud_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Analysis parameters based on successful investor patterns
        self.scoring_weights = {
            "roi_score": 0.30,           # Return on investment
            "price_score": 0.25,         # Price vs. optimal range
            "property_score": 0.20,      # Property characteristics
            "market_score": 0.15,        # Market conditions
            "risk_score": 0.10           # Risk assessment
        }
        
        # Cache for FMR data to reduce API calls
        self.fmr_cache = {}
    
    async def analyze_property(
        self, 
        property_data: Dict[str, Any],
        criteria_type: str = "primary"
    ) -> Optional[PropertyAnalysis]:
        """
        Comprehensive property analysis for Section 8 investment
        
        Args:
            property_data: Property information from API
            criteria_type: Investment criteria type (primary, opportunity, premium)
        
        Returns:
            PropertyAnalysis object or None if analysis fails
        """
        try:
            self.logger.debug(f"Analyzing property: {property_data.get('address')}")
            
            # Get investment criteria
            criteria = self.config.get_investment_criteria(criteria_type)
            
            # Basic property validation
            if not self._validate_basic_criteria(property_data, criteria):
                return None
            
            # Get detailed property information
            detailed_data = await self._get_detailed_property_data(property_data)
            if not detailed_data:
                detailed_data = property_data  # Fallback to basic data
            
            # Analyze market and get FMR data
            market_analysis = await self._analyze_market_conditions(detailed_data)
            
            # Calculate financial metrics
            financial_metrics = await self._calculate_financial_metrics(
                detailed_data, market_analysis, criteria
            )
            
            # Calculate investment score
            investment_score = self._calculate_investment_score(
                detailed_data, market_analysis, financial_metrics, criteria
            )
            
            # Determine investment priority
            investment_priority = self._determine_investment_priority(
                investment_score, financial_metrics
            )
            
            # Generate analysis reasons
            analysis_reasons = self._generate_analysis_reasons(
                detailed_data, market_analysis, financial_metrics, investment_score
            )
            
            # Identify risk factors
            risk_factors = self._identify_risk_factors(detailed_data, market_analysis)
            
            # Identify opportunity factors
            opportunity_factors = self._identify_opportunity_factors(
                detailed_data, market_analysis, financial_metrics
            )
            
            # Get comparable properties
            comparable_properties = await self._get_comparable_properties(detailed_data)
            
            # Create analysis result
            analysis = PropertyAnalysis(
                property_id=str(detailed_data.get('id', '')),
                address=detailed_data.get('address', ''),
                investment_score=investment_score,
                investment_priority=investment_priority,
                roi_percentage=financial_metrics.get('roi_percentage', 0),
                estimated_rent=financial_metrics.get('estimated_rent', 0),
                estimated_cash_flow=financial_metrics.get('estimated_cash_flow', 0),
                fair_market_rent=financial_metrics.get('fair_market_rent', 0),
                analysis_reasons=analysis_reasons,
                risk_factors=risk_factors,
                opportunity_factors=opportunity_factors,
                comparable_properties=comparable_properties or [],
                market_analysis=market_analysis
            )
            
            self.logger.info(
                f"Property analysis completed: {analysis.address} - "
                f"Score: {investment_score}/100, Priority: {investment_priority}"
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Property analysis failed: {str(e)}")
            return None
    
    def _validate_basic_criteria(
        self, 
        property_data: Dict[str, Any], 
        criteria: InvestmentCriteria
    ) -> bool:
        """Validate property against basic investment criteria"""
        
        # Price range validation
        estimated_value = property_data.get('estimatedValue', 0)
        if not (criteria.min_price <= estimated_value <= criteria.max_price):
            return False
        
        # Bedroom validation
        bedrooms = property_data.get('bedrooms', 0)
        if not (criteria.min_bedrooms <= bedrooms <= criteria.max_bedrooms):
            return False
        
        # Bathroom validation
        bathrooms = property_data.get('bathrooms', 0)
        if not (criteria.min_bathrooms <= bathrooms <= criteria.max_bathrooms):
            return False
        
        # Square footage validation
        square_feet = property_data.get('squareFeet', 0)
        if square_feet > 0:  # Only validate if data is available
            if not (criteria.min_square_feet <= square_feet <= criteria.max_square_feet):
                return False
        
        # Year built validation
        year_built = property_data.get('yearBuilt', 0)
        if year_built > 0:  # Only validate if data is available
            if year_built < criteria.min_year_built:
                return False
        
        # Property type validation
        property_type = property_data.get('propertyType', '')
        if property_type and property_type not in criteria.property_types:
            return False
        
        return True
    
    async def _get_detailed_property_data(self, property_data: Dict[str, Any]) -> Optional[Dict]:
        """Get detailed property information from API"""
        try:
            property_id = property_data.get('id')
            if property_id:
                detailed_data = await self.real_estate_client.get_property_details(property_id)
                if detailed_data:
                    return detailed_data
            
            # Fallback to address-based lookup
            address = property_data.get('address')
            if address:
                detailed_data = await self.real_estate_client.get_property_details(address)
                if detailed_data:
                    return detailed_data
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Could not get detailed property data: {str(e)}")
            return None
    
    async def _analyze_market_conditions(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market conditions for the property"""
        market_analysis = {
            "fmr_data": {},
            "market_strength": "UNKNOWN",
            "rent_potential": "UNKNOWN",
            "demographics": {},
            "hud_designations": []
        }
        
        try:
            # Extract location information
            state = property_data.get('state', '')
            city = property_data.get('city', '')
            zip_code = property_data.get('zip', '')
            
            # Get Fair Market Rent data
            if state and city:
                fmr_data = await self._get_fair_market_rent(state, city, zip_code)
                market_analysis["fmr_data"] = fmr_data
            
            # Extract demographics from property details
            demographics = property_data.get('demographics', {})
            if demographics:
                market_analysis["demographics"] = demographics
                
                # Check HUD designations
                hud_designations = demographics.get('hudDesignations', [])
                market_analysis["hud_designations"] = hud_designations
            
            # Analyze market strength based on available data
            market_analysis["market_strength"] = self._assess_market_strength(
                property_data, market_analysis
            )
            
        except Exception as e:
            self.logger.warning(f"Market analysis failed: {str(e)}")
        
        return market_analysis
    
    async def _get_fair_market_rent(
        self, 
        state: str, 
        city: str, 
        zip_code: str = None
    ) -> Dict[int, float]:
        """Get Fair Market Rent data with caching"""
        
        # Check cache first
        cache_key = f"{state}_{city}_{zip_code or 'no_zip'}"
        if cache_key in self.fmr_cache:
            return self.fmr_cache[cache_key]
        
        fmr_data = {}
        
        try:
            # Try Small Area FMR first (more accurate) if ZIP code available
            if zip_code:
                safmr_result = await self.hud_client.get_small_area_fmr(zip_code)
                if safmr_result:
                    fmr_data = self.hud_client.extract_bedroom_rents(safmr_result)
            
            # If no SAFMR data or no ZIP, try regular FMR
            if not fmr_data:
                fmr_result = await self.hud_client.get_fair_market_rents(state, city)
                if fmr_result:
                    fmr_data = self.hud_client.extract_bedroom_rents(fmr_result)
            
            # Fallback to estimated FMR if API fails
            if not fmr_data:
                for bedrooms in range(0, 6):
                    fmr_data[bedrooms] = self.hud_client.get_fallback_fmr(state, bedrooms)
            
            # Cache the result
            self.fmr_cache[cache_key] = fmr_data
            return fmr_data
            
        except Exception as e:
            self.logger.warning(f"FMR lookup failed for {state} {city}: {str(e)}")
            
            # Return fallback FMR data
            fallback_fmr = {}
            for bedrooms in range(0, 6):
                fallback_fmr[bedrooms] = self.hud_client.get_fallback_fmr(state, bedrooms)
            
            return fallback_fmr
    
    def _assess_market_strength(
        self, 
        property_data: Dict[str, Any], 
        market_analysis: Dict[str, Any]
    ) -> str:
        """Assess market strength based on available data"""
        
        strength_indicators = 0
        total_indicators = 0
        
        # Check demographics
        demographics = market_analysis.get("demographics", {})
        if demographics:
            # Population density indicator
            population = demographics.get('population', 0)
            if population > 50000:
                strength_indicators += 1
            total_indicators += 1
            
            # Income level indicator
            median_income = demographics.get('medianHouseholdIncome', 0)
            if 25000 <= median_income <= 65000:  # Good Section 8 income range
                strength_indicators += 1
            total_indicators += 1
        
        # Check HUD designations
        hud_designations = market_analysis.get("hud_designations", [])
        if hud_designations:
            strength_indicators += 1
        total_indicators += 1
        
        # Property characteristics
        if property_data.get('absenteeOwner'):
            strength_indicators += 1  # Indicates rental market
        total_indicators += 1
        
        # Calculate strength score
        if total_indicators > 0:
            strength_score = strength_indicators / total_indicators
            if strength_score >= 0.75:
                return "STRONG"
            elif strength_score >= 0.5:
                return "MODERATE"
            else:
                return "WEAK"
        
        return "UNKNOWN"
    
    async def _calculate_financial_metrics(
        self,
        property_data: Dict[str, Any],
        market_analysis: Dict[str, Any],
        criteria: InvestmentCriteria
    ) -> Dict[str, Any]:
        """Calculate financial metrics for investment analysis"""
        
        metrics = {
            "estimated_rent": 0,
            "fair_market_rent": 0,
            "estimated_cash_flow": 0,
            "roi_percentage": 0,
            "monthly_expenses": 0,
            "purchase_price": 0,
            "rent_to_price_ratio": 0
        }
        
        try:
            # Get property details
            purchase_price = property_data.get('estimatedValue', 0)
            bedrooms = property_data.get('bedrooms', 2)
            square_feet = property_data.get('squareFeet', 1000)
            
            # Get Fair Market Rent
            fmr_data = market_analysis.get("fmr_data", {})
            fair_market_rent = fmr_data.get(bedrooms, 0)
            
            # Estimate market rent (conservative approach)
            if fair_market_rent > 0:
                # Use 90% of FMR as conservative estimate
                estimated_rent = fair_market_rent * 0.90
            else:
                # Fallback calculation based on square footage and location
                rent_per_sqft = self._estimate_rent_per_sqft(property_data)
                estimated_rent = square_feet * rent_per_sqft
            
            # Calculate monthly expenses (conservative estimates)
            monthly_expenses = self._calculate_monthly_expenses(
                purchase_price, property_data
            )
            
            # Calculate cash flow
            estimated_cash_flow = estimated_rent - monthly_expenses
            
            # Calculate ROI
            if purchase_price > 0:
                annual_cash_flow = estimated_cash_flow * 12
                roi_percentage = (annual_cash_flow / purchase_price) * 100
                rent_to_price_ratio = (estimated_rent * 12) / purchase_price * 100
            else:
                roi_percentage = 0
                rent_to_price_ratio = 0
            
            # Update metrics
            metrics.update({
                "estimated_rent": estimated_rent,
                "fair_market_rent": fair_market_rent,
                "estimated_cash_flow": estimated_cash_flow,
                "roi_percentage": roi_percentage,
                "monthly_expenses": monthly_expenses,
                "purchase_price": purchase_price,
                "rent_to_price_ratio": rent_to_price_ratio
            })
            
        except Exception as e:
            self.logger.error(f"Financial metrics calculation failed: {str(e)}")
        
        return metrics
    
    def _estimate_rent_per_sqft(self, property_data: Dict[str, Any]) -> float:
        """Estimate rent per square foot based on location and property type"""
        
        # Base rent per sqft by state (conservative estimates)
        state_rent_rates = {
            "MI": 0.75,  # Michigan
            "OH": 0.70,  # Ohio
            "PA": 0.80,  # Pennsylvania
            "NY": 1.10,  # New York (upstate)
            "WI": 0.75,  # Wisconsin
            "IN": 0.65,  # Indiana
            "AL": 0.60,  # Alabama
            "TN": 0.70,  # Tennessee
            "MS": 0.55,  # Mississippi
            "LA": 0.65,  # Louisiana
            "AR": 0.60,  # Arkansas
        }
        
        state = property_data.get('state', '').upper()
        base_rate = state_rent_rates.get(state, 0.70)  # Default conservative rate
        
        # Adjust based on property type
        property_type = property_data.get('propertyType', '')
        if property_type == 'SINGLE_FAMILY':
            multiplier = 1.0
        elif property_type == 'MULTI_FAMILY':
            multiplier = 0.9
        elif property_type == 'TOWNHOUSE':
            multiplier = 0.95
        elif property_type == 'CONDO':
            multiplier = 0.85
        else:
            multiplier = 0.9
        
        return base_rate * multiplier
    
    def _calculate_monthly_expenses(
        self, 
        purchase_price: int, 
        property_data: Dict[str, Any]
    ) -> float:
        """Calculate estimated monthly expenses (conservative)"""
        
        # Conservative expense estimates as percentages of rent/value
        expenses = {
            "property_taxes": purchase_price * 0.015 / 12,  # 1.5% annually
            "insurance": purchase_price * 0.008 / 12,       # 0.8% annually
            "maintenance": purchase_price * 0.01 / 12,      # 1% annually
            "vacancy": 0,  # Will be calculated based on rent
            "property_management": 0,  # Will be calculated based on rent
            "utilities": 50,  # Base utilities if landlord pays
        }
        
        # Property management (8% of rent - estimated)
        estimated_rent = purchase_price * 0.01  # Rough estimate for calculation
        expenses["property_management"] = estimated_rent * 0.08
        
        # Vacancy allowance (5% of rent)
        expenses["vacancy"] = estimated_rent * 0.05
        
        total_monthly_expenses = sum(expenses.values())
        
        return total_monthly_expenses
    
    def _calculate_investment_score(
        self,
        property_data: Dict[str, Any],
        market_analysis: Dict[str, Any],
        financial_metrics: Dict[str, Any],
        criteria: InvestmentCriteria
    ) -> int:
        """Calculate comprehensive investment score (0-100)"""
        
        # Individual scores
        scores = {
            "roi_score": self._calculate_roi_score(financial_metrics, criteria),
            "price_score": self._calculate_price_score(property_data, criteria),
            "property_score": self._calculate_property_score(property_data, criteria),
            "market_score": self._calculate_market_score(market_analysis),
            "risk_score": self._calculate_risk_score(property_data, market_analysis)
        }
        
        # Weighted final score
        final_score = sum(
            scores[component] * weight 
            for component, weight in self.scoring_weights.items()
        )
        
        # Ensure score is between 0 and 100
        final_score = max(0, min(100, int(final_score)))
        
        self.logger.debug(f"Investment scores: {scores} -> Final: {final_score}")
        
        return final_score
    
    def _calculate_roi_score(
        self, 
        financial_metrics: Dict[str, Any], 
        criteria: InvestmentCriteria
    ) -> float:
        """Calculate ROI-based score (0-100)"""
        
        roi_percentage = financial_metrics.get('roi_percentage', 0)
        
        if roi_percentage >= criteria.target_roi_percentage:
            return 100  # Excellent ROI
        elif roi_percentage >= criteria.min_roi_percentage:
            # Scale between min and target
            range_size = criteria.target_roi_percentage - criteria.min_roi_percentage
            score_range = roi_percentage - criteria.min_roi_percentage
            return 60 + (score_range / range_size) * 40  # 60-100 range
        elif roi_percentage >= 0:
            # Below minimum but positive
            return (roi_percentage / criteria.min_roi_percentage) * 60  # 0-60 range
        else:
            return 0  # Negative ROI
    
    def _calculate_price_score(
        self, 
        property_data: Dict[str, Any], 
        criteria: InvestmentCriteria
    ) -> float:
        """Calculate price-based score (0-100)"""
        
        estimated_value = property_data.get('estimatedValue', 0)
        
        # Optimal price range based on Dustin's successful pattern
        optimal_min = 80000
        optimal_max = 150000
        
        if optimal_min <= estimated_value <= optimal_max:
            return 100  # Perfect price range
        elif criteria.min_price <= estimated_value <= criteria.max_price:
            # Within acceptable range but not optimal
            if estimated_value < optimal_min:
                # Below optimal (opportunity zone)
                return 60 + ((estimated_value - criteria.min_price) / (optimal_min - criteria.min_price)) * 30
            else:
                # Above optimal (premium zone)
                return 60 + ((criteria.max_price - estimated_value) / (criteria.max_price - optimal_max)) * 30
        else:
            return 0  # Outside acceptable range
    
    def _calculate_property_score(
        self, 
        property_data: Dict[str, Any], 
        criteria: InvestmentCriteria
    ) -> float:
        """Calculate property characteristics score (0-100)"""
        
        score = 0
        max_score = 100
        
        # Bedroom count scoring (3BR is optimal for Section 8)
        bedrooms = property_data.get('bedrooms', 0)
        if bedrooms == 3:
            score += 25  # Optimal
        elif bedrooms == 2:
            score += 20  # Good
        elif bedrooms == 4:
            score += 15  # Acceptable
        elif bedrooms == 1:
            score += 10  # Limited appeal
        
        # Year built scoring (newer is better for Section 8 standards)
        year_built = property_data.get('yearBuilt', 0)
        current_year = datetime.now().year
        if year_built >= 2000:
            score += 20  # Modern
        elif year_built >= 1990:
            score += 15  # Recent
        elif year_built >= 1980:
            score += 12  # Acceptable
        elif year_built >= 1970:
            score += 8   # Minimum Section 8 standard
        
        # Square footage scoring
        square_feet = property_data.get('squareFeet', 0)
        if 1000 <= square_feet <= 1500:
            score += 15  # Optimal size
        elif 800 <= square_feet <= 2000:
            score += 12  # Good size
        elif square_feet > 0:
            score += 8   # Has size data
        
        # Property type scoring
        property_type = property_data.get('propertyType', '')
        if property_type == 'SINGLE_FAMILY':
            score += 15  # Preferred for Section 8
        elif property_type == 'TOWNHOUSE':
            score += 12  # Good option
        elif property_type == 'MULTI_FAMILY':
            score += 10  # Acceptable
        
        # Investment indicators
        if property_data.get('absenteeOwner'):
            score += 10  # Indicates rental property
        if property_data.get('vacant'):
            score += 5   # Ready for tenant
        
        # Condition indicators
        if property_data.get('foreclosure'):
            score += 5   # Potential opportunity (but risky)
        
        return min(score, max_score)
    
    def _calculate_market_score(self, market_analysis: Dict[str, Any]) -> float:
        """Calculate market conditions score (0-100)"""
        
        score = 0
        
        # Market strength
        market_strength = market_analysis.get("market_strength", "UNKNOWN")
        if market_strength == "STRONG":
            score += 30
        elif market_strength == "MODERATE":
            score += 20
        elif market_strength == "WEAK":
            score += 10
        
        # HUD designations
        hud_designations = market_analysis.get("hud_designations", [])
        if hud_designations:
            score += 20
        
        # Demographics
        demographics = market_analysis.get("demographics", {})
        if demographics:
            score += 15
            
            # Income level check
            median_income = demographics.get('medianHouseholdIncome', 0)
            if 25000 <= median_income <= 65000:
                score += 15  # Good Section 8 income range
        
        # FMR data availability
        fmr_data = market_analysis.get("fmr_data", {})
        if fmr_data:
            score += 20
        
        return min(score, 100)
    
    def _calculate_risk_score(
        self, 
        property_data: Dict[str, Any], 
        market_analysis: Dict[str, Any]
    ) -> float:
        """Calculate risk assessment score (0-100, higher is lower risk)"""
        
        score = 100  # Start with perfect score, deduct for risks
        
        # Age-related risks
        year_built = property_data.get('yearBuilt', 0)
        current_year = datetime.now().year
        age = current_year - year_built if year_built > 0 else 50
        
        if age > 50:
            score -= 30  # High maintenance risk
        elif age > 30:
            score -= 15  # Moderate maintenance risk
        elif age > 20:
            score -= 5   # Minor maintenance risk
        
        # Vacancy risk
        if property_data.get('vacant'):
            score -= 10  # May need work
        
        # Foreclosure risk/opportunity
        if property_data.get('foreclosure'):
            score -= 15  # Higher risk but potential opportunity
        
        # Market risks
        market_strength = market_analysis.get("market_strength", "UNKNOWN")
        if market_strength == "WEAK":
            score -= 20
        elif market_strength == "UNKNOWN":
            score -= 10
        
        # Size risks
        square_feet = property_data.get('squareFeet', 0)
        if square_feet < 800:
            score -= 15  # Too small for families
        elif square_feet > 2500:
            score -= 10  # May be expensive to maintain
        
        return max(0, score)
    
    def _determine_investment_priority(
        self, 
        investment_score: int, 
        financial_metrics: Dict[str, Any]
    ) -> str:
        """Determine investment priority based on score and ROI"""
        
        roi_percentage = financial_metrics.get('roi_percentage', 0)
        
        if investment_score >= 70 and roi_percentage >= 12:
            return "HIGH"
        elif investment_score >= 50 and roi_percentage >= 8:
            return "MEDIUM"
        elif investment_score >= 30 or roi_percentage >= 5:
            return "LOW"
        else:
            return "SKIP"
    
    def _generate_analysis_reasons(
        self,
        property_data: Dict[str, Any],
        market_analysis: Dict[str, Any],
        financial_metrics: Dict[str, Any],
        investment_score: int
    ) -> List[str]:
        """Generate human-readable analysis reasons"""
        
        reasons = []
        
        # ROI reasons
        roi_percentage = financial_metrics.get('roi_percentage', 0)
        if roi_percentage >= 15:
            reasons.append(f"Excellent ROI: {roi_percentage:.1f}%")
        elif roi_percentage >= 10:
            reasons.append(f"Strong ROI: {roi_percentage:.1f}%")
        elif roi_percentage >= 8:
            reasons.append(f"Good ROI: {roi_percentage:.1f}%")
        
        # Price reasons
        estimated_value = property_data.get('estimatedValue', 0)
        if 80000 <= estimated_value <= 150000:
            reasons.append("Optimal Section 8 price range")
        elif estimated_value < 80000:
            reasons.append("Below-market opportunity price")
        
        # Property characteristics
        bedrooms = property_data.get('bedrooms', 0)
        if bedrooms == 3:
            reasons.append("3BR - highest Section 8 demand")
        elif bedrooms == 2:
            reasons.append("2BR - strong Section 8 demand")
        
        # Investment indicators
        if property_data.get('absenteeOwner'):
            reasons.append("Absentee owner - potential motivated seller")
        
        if property_data.get('vacant'):
            reasons.append("Vacant - ready for immediate rental")
        
        # Year built
        year_built = property_data.get('yearBuilt', 0)
        if year_built >= 1980:
            reasons.append("Built 1980+ - meets Section 8 standards")
        
        # Market factors
        market_strength = market_analysis.get("market_strength")
        if market_strength == "STRONG":
            reasons.append("Strong rental market")
        
        hud_designations = market_analysis.get("hud_designations", [])
        if hud_designations:
            reasons.append("HUD designated area")
        
        # Cash flow
        cash_flow = financial_metrics.get('estimated_cash_flow', 0)
        if cash_flow > 200:
            reasons.append(f"Strong cash flow: ${cash_flow:.0f}/month")
        elif cash_flow > 0:
            reasons.append(f"Positive cash flow: ${cash_flow:.0f}/month")
        
        return reasons
    
    def _identify_risk_factors(
        self,
        property_data: Dict[str, Any],
        market_analysis: Dict[str, Any]
    ) -> List[str]:
        """Identify potential risk factors"""
        
        risks = []
        
        # Age-related risks
        year_built = property_data.get('yearBuilt', 0)
        current_year = datetime.now().year
        age = current_year - year_built if year_built > 0 else 50
        
        if age > 50:
            risks.append("Property age 50+ years - higher maintenance costs")
        elif age > 30:
            risks.append("Property age 30+ years - moderate maintenance expected")
        
        # Market risks
        market_strength = market_analysis.get("market_strength")
        if market_strength == "WEAK":
            risks.append("Weak rental market conditions")
        elif market_strength == "UNKNOWN":
            risks.append("Limited market data available")
        
        # Property condition risks
        if property_data.get('foreclosure'):
            risks.append("Foreclosure property - may need significant repairs")
        
        # Size risks
        square_feet = property_data.get('squareFeet', 0)
        if square_feet < 800:
            risks.append("Small size may limit tenant pool")
        elif square_feet > 2500:
            risks.append("Large size may increase maintenance costs")
        
        # Financial risks
        estimated_value = property_data.get('estimatedValue', 0)
        if estimated_value > 200000:
            risks.append("Higher purchase price reduces cash-on-cash returns")
        
        return risks
    
    def _identify_opportunity_factors(
        self,
        property_data: Dict[str, Any],
        market_analysis: Dict[str, Any],
        financial_metrics: Dict[str, Any]
    ) -> List[str]:
        """Identify opportunity factors"""
        
        opportunities = []
        
        # Price opportunities
        estimated_value = property_data.get('estimatedValue', 0)
        if estimated_value < 100000:
            opportunities.append("Below $100K - excellent cash flow potential")
        
        # Market opportunities
        hud_designations = market_analysis.get("hud_designations", [])
        if hud_designations:
            opportunities.append("HUD designated area - guaranteed Section 8 demand")
        
        # Property opportunities
        if property_data.get('absenteeOwner'):
            opportunities.append("Absentee owner may be motivated to sell")
        
        if property_data.get('vacant'):
            opportunities.append("Vacant property - immediate rental income potential")
        
        # Financial opportunities
        rent_to_price_ratio = financial_metrics.get('rent_to_price_ratio', 0)
        if rent_to_price_ratio > 1.2:  # 1.2% rule
            opportunities.append("Exceeds 1.2% rule - excellent investment")
        elif rent_to_price_ratio > 1.0:  # 1% rule
            opportunities.append("Meets 1% rule - good investment")
        
        return opportunities
    
    async def _get_comparable_properties(
        self, 
        property_data: Dict[str, Any]
    ) -> Optional[List[Dict]]:
        """Get comparable properties for analysis"""
        try:
            property_id = property_data.get('id')
            if property_id:
                comps_data = await self.real_estate_client.get_property_comps(property_id)
                if comps_data and 'comps' in comps_data:
                    return comps_data['comps'][:5]  # Return top 5 comps
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Could not get comparable properties: {str(e)}")
            return None
