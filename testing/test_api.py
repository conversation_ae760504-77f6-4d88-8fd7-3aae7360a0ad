#!/usr/bin/env python3
"""
Simple Real Estate API Test Script
Test individual property lookups to understand the data structure
"""

import requests
import json

# API Configuration
API_KEY = "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
BASE_URL = "https://api.realestateapi.com"

headers = {
    "Content-Type": "application/json",
    "x-api-key": f"{API_KEY}"
}

def test_property_detail(address):
    """Test Property Detail API with a single address"""
    url = f"{BASE_URL}/v2/PropertyDetail"
    
    payload = {
        "address": address
    }
    
    print(f"Testing Property Detail API for: {address}")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("-" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ SUCCESS!")
            
            # Extract key Section 8 relevant information
            print("\\nKey Information:")
            print(f"Property ID: {data.get('id')}")
            print(f"Property Type: {data.get('propertyType')}")
            
            # Property details
            prop_info = data.get('propertyInfo', {})
            print(f"Bedrooms: {prop_info.get('bedrooms')}")
            print(f"Bathrooms: {prop_info.get('bathrooms')}")
            print(f"Square Feet: {prop_info.get('squareFeet')}")
            print(f"Year Built: {prop_info.get('yearBuilt')}")
            
            # Demographics and Fair Market Rent
            demographics = data.get('demographics', {})
            if demographics:
                print("\\nDemographics & Fair Market Rent:")
                print(f"Median Household Income: ${demographics.get('medianHouseholdIncome', 'N/A')}")
                
                fmr = demographics.get('fairMarketRent', {})
                if fmr:
                    print("Fair Market Rents:")
                    print(f"  1BR: ${fmr.get('oneBedroom', 'N/A')}")
                    print(f"  2BR: ${fmr.get('twoBedroom', 'N/A')}")
                    print(f"  3BR: ${fmr.get('threeBedroom', 'N/A')}")
                    print(f"  4BR: ${fmr.get('fourBedroom', 'N/A')}")
                
                hud_designations = demographics.get('hudDesignations', [])
                if hud_designations:
                    print(f"HUD Designations: {', '.join(hud_designations)}")
            
            # Owner information
            owner_info = data.get('ownerInfo', {})
            print(f"\\nOwner Information:")
            print(f"Absentee Owner: {data.get('absenteeOwner', 'N/A')}")
            print(f"Owner Occupied: {data.get('ownerOccupied', 'N/A')}")
            print(f"Years Owned: {owner_info.get('yearsOwned', 'N/A')}")
            
            # Save full response for analysis
            filename = f"property_detail_{address.replace(' ', '_').replace(',', '')}.json"
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            print(f"\\nFull response saved to: {filename}")
            
        else:
            print(f"✗ ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"✗ EXCEPTION: {str(e)}")

def test_property_search():
    """Test Property Search API to find properties in New Bern area"""
    url = f"{BASE_URL}/v2/PropertySearch"
    
    payload = {
        "state": "NC",
        "limit": 10,
        "property_type": "SFR"
    }
    
    print(f"\\nTesting Property Search API")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("-" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ SUCCESS!")
            
            properties = data.get('data', [])
            print(f"Found {len(properties)} properties")
            
            for i, prop in enumerate(properties[:3]):  # Show first 3
                print(f"\\nProperty {i+1}:")
                print(f"  Address: {prop.get('address')}")
                print(f"  Bedrooms: {prop.get('bedrooms')}")
                print(f"  Bathrooms: {prop.get('bathrooms')}")
                print(f"  Sq Ft: {prop.get('squareFeet')}")
                print(f"  Estimated Value: ${prop.get('estimatedValue', 0):,}")
            
            # Save search results
            with open('property_search_results.json', 'w') as f:
                json.dump(data, f, indent=2)
            print(f"\\nSearch results saved to: property_search_results.json")
            
        else:
            print(f"✗ ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"✗ EXCEPTION: {str(e)}")

if __name__ == "__main__":
    print("Real Estate API Test Script")
    print("="*50)
    
    # Test with one of Dustin's properties
    test_address = "407 HOKE ST, NEW BERN, NC 28560"
    test_property_detail(test_address)
    
    # Test property search
    test_property_search()
    
    print("\\n" + "="*50)
    print("Test completed!")
