#!/usr/bin/env python3
"""
Manual Analysis of <PERSON>'s Properties Based on Transaction Data
This script analyzes the purchase patterns without needing API calls
"""

import json
from datetime import datetime

# <PERSON>'s property transactions from the provided data
properties_data = [
    {"address": "102 RIVER BLUFFS DR, NEW BERN, NC 28560", "price": 375000, "date": "07/14/2022"},
    {"address": "107 STILLWOOD CT, NEW BERN, NC 28560", "price": None, "date": None},
    {"address": "407 HOKE ST, NEW BERN, NC 28560", "price": 115000, "date": "01/04/2007"},
    {"address": "2 SUNSET AVE # B, WRIGHTSVILLE BEACH, NC 28480", "price": 1735000, "date": "07/02/2020"},
    {"address": "2331 CHINQUAPIN RD, NEW BERN, NC 28562", "price": 837500, "date": "02/20/2006"},
    {"address": "208 BUCKSKIN DR, NEW BERN, NC 28562", "price": 130000, "date": "09/10/2020"},
    {"address": "416 SWISS RD, NEW BERN, NC 28560", "price": 135000, "date": "12/23/2020"},
    {"address": "3408 OLD CHERRY POINT RD, NEW BERN, NC 28560", "price": 185000, "date": "09/23/2018"},
    {"address": "712 MADAM MOORES LN, NEW BERN, NC 28562", "price": 1750000, "date": "10/30/2023"},
    {"address": "2400 NEUSE BLVD, NEW BERN, NC 28562", "price": 675000, "date": "01/02/2023"},
    {"address": "1150 US 70 HWY E, NEW BERN, NC 28560", "price": 600000, "date": "12/14/2017"},
    {"address": "3111 EVANS ST, MOREHEAD CITY, NC 28557", "price": 619000, "date": "06/12/2018"},
    {"address": "608 EMERALD DR, EMERALD ISLE, NC 28594", "price": 705000, "date": "12/20/2022"},
    {"address": "1503 FAIRFAX LN, NEW BERN, NC 28562", "price": 122000, "date": "02/27/2023"},
    {"address": "171 PERRYTOWN RD, NEW BERN, NC 28562", "price": 362500, "date": "12/14/2018"},
    {"address": "3416 OLD CHERRY POINT RD, NEW BERN, NC 28560", "price": 220000, "date": "01/19/2006"},
    {"address": "202 OCEAN BLVD, ATLANTIC BEACH, NC 28512", "price": 1100000, "date": "01/21/2021"},
    {"address": "2400 RICHMOND CT, NEW BERN, NC 28562", "price": 83000, "date": "08/29/2017"},
    {"address": "101 PERRYTOWN RD, NEW BERN, NC 28562", "price": 400000, "date": "09/27/2024"},
    {"address": "132 TRENT SHORES DR, TRENT WOODS, NC 28562", "price": 406500, "date": "03/08/2024"},
    {"address": "131 NYON RD, NEW BERN, NC 28562", "price": 397500, "date": "02/22/2024"},
    {"address": "5222 MOYE RD, TRENT WOODS, NC 28562", "price": 300000, "date": "11/12/2021"}
]

def analyze_purchase_patterns():
    """Analyze Dustin's property purchase patterns"""
    
    print("DUSTIN'S PROPERTY PORTFOLIO ANALYSIS")
    print("="*50)
    
    # Filter properties with valid price data
    valid_properties = [p for p in properties_data if p['price'] is not None]
    
    print(f"Total Properties: {len(properties_data)}")
    print(f"Properties with Price Data: {len(valid_properties)}")
    
    # Price Analysis
    prices = [p['price'] for p in valid_properties]
    avg_price = sum(prices) / len(prices)
    min_price = min(prices)
    max_price = max(prices)
    
    print(f"\\n💰 PRICE ANALYSIS:")
    print(f"Average Purchase Price: ${avg_price:,.0f}")
    print(f"Price Range: ${min_price:,} - ${max_price:,}")
    
    # Categorize properties by likely use
    section8_candidates = []
    luxury_properties = []
    mid_range_properties = []
    
    for prop in valid_properties:
        if prop['price'] <= 200000:
            section8_candidates.append(prop)
        elif prop['price'] >= 500000:
            luxury_properties.append(prop)
        else:
            mid_range_properties.append(prop)
    
    print(f"\\n🏠 PROPERTY CATEGORIES:")
    print(f"Likely Section 8 Properties (≤$200K): {len(section8_candidates)}")
    print(f"Mid-Range Properties ($200K-$500K): {len(mid_range_properties)}")
    print(f"Luxury Properties (≥$500K): {len(luxury_properties)}")
    
    # Section 8 Property Analysis
    print(f"\\n🎯 SECTION 8 PROPERTY CANDIDATES:")
    print("-" * 40)
    
    if section8_candidates:
        for prop in sorted(section8_candidates, key=lambda x: x['price']):
            print(f"${prop['price']:,} - {prop['address']} ({prop['date']})")
        
        s8_prices = [p['price'] for p in section8_candidates]
        s8_avg = sum(s8_prices) / len(s8_prices)
        print(f"\\nSection 8 Average Price: ${s8_avg:,.0f}")
    
    # Geographic Analysis
    print(f"\\n🗺️ GEOGRAPHIC DISTRIBUTION:")
    print("-" * 40)
    
    locations = {}
    for prop in properties_data:
        city = prop['address'].split(',')[1].strip()
        locations[city] = locations.get(city, 0) + 1
    
    for city, count in sorted(locations.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / len(properties_data)) * 100
        print(f"{city}: {count} properties ({percentage:.1f}%)")
    
    # ZIP Code Analysis
    print(f"\\n📮 ZIP CODE ANALYSIS:")
    print("-" * 40)
    
    zip_codes = {}
    for prop in properties_data:
        zip_code = prop['address'][-5:]
        zip_codes[zip_code] = zip_codes.get(zip_code, 0) + 1
    
    for zip_code, count in sorted(zip_codes.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / len(properties_data)) * 100
        print(f"{zip_code}: {count} properties ({percentage:.1f}%)")
    
    # Timeline Analysis
    print(f"\\n📅 PURCHASE TIMELINE:")
    print("-" * 40)
    
    properties_with_dates = [p for p in valid_properties if p['date']]
    properties_with_dates.sort(key=lambda x: datetime.strptime(x['date'], '%m/%d/%Y'))
    
    purchase_years = {}
    for prop in properties_with_dates:
        year = prop['date'][-4:]
        purchase_years[year] = purchase_years.get(year, 0) + 1
    
    for year, count in sorted(purchase_years.items()):
        print(f"{year}: {count} properties")
    
    # Investment Strategy Insights
    print(f"\\n💡 INVESTMENT STRATEGY INSIGHTS:")
    print("-" * 40)
    
    new_bern_properties = [p for p in valid_properties if 'NEW BERN' in p['address']]
    new_bern_section8 = [p for p in section8_candidates if 'NEW BERN' in p['address']]
    
    print(f"New Bern Focus: {len(new_bern_properties)}/{len(valid_properties)} properties ({len(new_bern_properties)/len(valid_properties)*100:.1f}%)")
    print(f"New Bern Section 8: {len(new_bern_section8)}/{len(section8_candidates)} candidates")
    
    if new_bern_section8:
        nb_s8_avg = sum(p['price'] for p in new_bern_section8) / len(new_bern_section8)
        print(f"New Bern Section 8 Avg Price: ${nb_s8_avg:,.0f}")
    
    # Recommendations
    print(f"\\n🎯 SECTION 8 PROPERTY CHARACTERISTICS:")
    print("-" * 40)
    print("Based on Dustin's portfolio, look for:")
    print(f"• Purchase Price: ${min([p['price'] for p in section8_candidates]):,} - ${max([p['price'] for p in section8_candidates]):,}")
    print("• Location: New Bern, NC area (especially ZIP 28560, 28562)")
    print("• Property Type: Single-family homes")
    print("• Strategy: Geographic clustering for easier management")
    
    return {
        'section8_candidates': section8_candidates,
        'geographic_distribution': locations,
        'zip_code_distribution': zip_codes,
        'average_section8_price': sum(p['price'] for p in section8_candidates) / len(section8_candidates) if section8_candidates else 0
    }

if __name__ == "__main__":
    results = analyze_purchase_patterns()
    
    # Save results
    with open('manual_analysis_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\\nResults saved to: manual_analysis_results.json")
