#!/usr/bin/env python3
"""
LLC Property Discovery Script
Find hidden property holdings through LLC ownership analysis
"""

import requests
import json
import time
from datetime import datetime
import pandas as pd

# API Configuration
API_KEY = "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
BASE_URL = "https://api.realestateapi.com"

headers = {
    "Content-Type": "application/json",
    "x-api-key": f"{API_KEY}"
}

# Known LLC-owned properties from transaction data
known_llc_properties = {
    "NC ILM PROPERTIES LLC": [
        "1109 FANNING ST, WILMINGTON, NC 28401",
        "317 QUEEN ST, WILMINGTON, NC 28401", 
        "720 N 11TH ST, WILMINGTON, NC 28401",
        "2728 WORTH DR, WILMINGTON, NC 28412",
        "819 WOOSTER ST, WILMINGTON, NC 28401"
    ],
    "SO EASY LLC": [
        "712 MADAM MOORES LN, NEW BERN, NC 28562"
    ],
    "GAGE PROPERTIES LLC": [
        "2400 NEUSE BLVD, NEW BERN, NC 28562"
    ],
    "CAR MANAGEMENT LLC": [
        "1150 US 70 HWY E, NEW BERN, NC 28560"
    ],
    "BIRDHOUSE HOLDINGS LLC": [
        "903 SIMMONS ST, NEW BERN, NC 28560"
    ],
    "FERN FOUNDATION LLC": [
        "301 COMMERCE WAY, ATLANTIC BEACH, NC 28512"
    ],
    "NEUSE TRENT HOLDINGS I LLC": [
        "3584 RED OAK DR, NEW BERN, NC 28562"
    ]
}

def get_property_details(address):
    """Get detailed property information including owner details"""
    url = f"{BASE_URL}/v2/PropertyDetail"
    
    payload = {"address": address}
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error getting details for {address}: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"Exception for {address}: {str(e)}")
        return None

def search_properties_in_area(city, state, property_params=None):
    """Search for properties in a specific area with optional filters"""
    url = f"{BASE_URL}/v2/PropertySearch"
    
    payload = {
        "city": city,
        "state": state,
        "limit": 100  # Get more results to find patterns
    }
    
    # Add additional search parameters if provided
    if property_params:
        payload.update(property_params)
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error searching {city}, {state}: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"Exception searching {city}, {state}: {str(e)}")
        return None

def analyze_llc_ownership_patterns():
    """Analyze known LLC properties to identify ownership patterns"""
    
    print("ANALYZING LLC PROPERTY OWNERSHIP PATTERNS")
    print("="*60)
    
    all_llc_data = {}
    
    for llc_name, properties in known_llc_properties.items():
        print(f"\\n🏢 Analyzing {llc_name}:")
        print(f"Known Properties: {len(properties)}")
        
        llc_properties_data = []
        
        for address in properties:
            print(f"  Fetching details: {address}")
            details = get_property_details(address)
            
            if details:
                # Extract key information
                property_info = {
                    'address': address,
                    'property_id': details.get('id'),
                    'property_type': details.get('propertyType'),
                    'estimated_value': details.get('estimatedValue'),
                    'bedrooms': details.get('propertyInfo', {}).get('bedrooms'),
                    'bathrooms': details.get('propertyInfo', {}).get('bathrooms'),
                    'square_feet': details.get('propertyInfo', {}).get('squareFeet'),
                    'year_built': details.get('propertyInfo', {}).get('yearBuilt'),
                    'lot_square_feet': details.get('lotInfo', {}).get('lotSquareFeet'),
                    'owner_info': details.get('ownerInfo', {})
                }
                
                # Check if owner name matches our LLC
                owner_name = f"{property_info['owner_info'].get('owner1FirstName', '')} {property_info['owner_info'].get('owner1LastName', '')}".strip()
                if owner_name:
                    property_info['owner_name'] = owner_name
                
                llc_properties_data.append(property_info)
                
            time.sleep(1)  # Be respectful to API
        
        all_llc_data[llc_name] = llc_properties_data
        
        # Analyze patterns for this LLC
        if llc_properties_data:
            print(f"  ✓ Retrieved data for {len(llc_properties_data)} properties")
            
            # Geographic clustering
            cities = [prop['address'].split(',')[1].strip() for prop in llc_properties_data]
            city_counts = {}
            for city in cities:
                city_counts[city] = city_counts.get(city, 0) + 1
            
            print(f"  Geographic Focus: {dict(city_counts)}")
            
            # Property value analysis
            values = [prop['estimated_value'] for prop in llc_properties_data if prop['estimated_value']]
            if values:
                avg_value = sum(values) / len(values)
                print(f"  Average Property Value: ${avg_value:,.0f}")
            
            # Property types
            types = [prop['property_type'] for prop in llc_properties_data if prop['property_type']]
            type_counts = {}
            for ptype in types:
                type_counts[ptype] = type_counts.get(ptype, 0) + 1
            
            if type_counts:
                print(f"  Property Types: {dict(type_counts)}")
    
    return all_llc_data

def search_for_additional_llc_properties():
    """Search for additional properties that might be owned by the same LLCs"""
    
    print("\\n" + "="*60)
    print("SEARCHING FOR ADDITIONAL LLC PROPERTIES")
    print("="*60)
    
    # Target areas where we know LLCs operate
    target_areas = [
        {"city": "WILMINGTON", "state": "NC"},  # Tom's NC ILM PROPERTIES
        {"city": "NEW BERN", "state": "NC"},    # Dustin's various LLCs
        {"city": "ATLANTIC BEACH", "state": "NC"}  # Dustin's coastal properties
    ]
    
    potential_llc_properties = []
    
    for area in target_areas:
        print(f"\\n🔍 Searching {area['city']}, {area['state']}...")
        
        # Search for absentee owner properties (common for LLC ownership)
        search_params = {
            "absentee_owner": True,
            "limit": 50
        }
        
        results = search_properties_in_area(area['city'], area['state'], search_params)
        
        if results and 'data' in results:
            properties = results['data']
            print(f"  Found {len(properties)} absentee owner properties")
            
            # Analyze properties for LLC characteristics
            for prop in properties:
                # Look for properties that match known patterns
                estimated_value = prop.get('estimatedValue', 0)
                
                # Tom's properties are high-value commercial ($1M+)
                # Dustin's LLC properties vary ($100K-$1.7M)
                
                if area['city'] == 'WILMINGTON' and estimated_value > 500000:
                    # Potential Tom/NC ILM PROPERTIES LLC property
                    prop['potential_llc'] = 'NC ILM PROPERTIES LLC'
                    prop['confidence'] = 'HIGH' if estimated_value > 1000000 else 'MEDIUM'
                    potential_llc_properties.append(prop)
                    
                elif area['city'] == 'NEW BERN':
                    # Potential Dustin LLC properties
                    if 100000 <= estimated_value <= 2000000:
                        prop['potential_llc'] = 'DUSTIN_LLC_NETWORK'
                        prop['confidence'] = 'MEDIUM'
                        potential_llc_properties.append(prop)
        
        time.sleep(2)  # Be respectful to API
    
    return potential_llc_properties

def generate_llc_discovery_report(llc_data, potential_properties):
    """Generate comprehensive LLC ownership report"""
    
    print("\\n" + "="*60)
    print("LLC PROPERTY DISCOVERY REPORT")
    print("="*60)
    
    # Known LLC Properties Summary
    print("\\n📋 CONFIRMED LLC PROPERTIES:")
    print("-" * 50)
    
    total_known_value = 0
    total_known_properties = 0
    
    for llc_name, properties in llc_data.items():
        if properties:
            llc_value = sum(prop.get('estimated_value', 0) for prop in properties if prop.get('estimated_value'))
            total_known_value += llc_value
            total_known_properties += len(properties)
            
            print(f"\\n{llc_name}:")
            print(f"  Properties: {len(properties)}")
            print(f"  Total Estimated Value: ${llc_value:,.0f}")
            
            for prop in properties:
                value = prop.get('estimated_value', 0)
                print(f"    • {prop['address']} - ${value:,.0f}")
    
    print(f"\\n📊 TOTAL KNOWN LLC PORTFOLIO:")
    print(f"  Total Properties: {total_known_properties}")
    print(f"  Total Estimated Value: ${total_known_value:,.0f}")
    
    # Potential Additional Properties
    print("\\n🔍 POTENTIAL ADDITIONAL LLC PROPERTIES:")
    print("-" * 50)
    
    if potential_properties:
        # Group by confidence level
        high_confidence = [p for p in potential_properties if p.get('confidence') == 'HIGH']
        medium_confidence = [p for p in potential_properties if p.get('confidence') == 'MEDIUM']
        
        print(f"\\nHigh Confidence Matches: {len(high_confidence)}")
        for prop in high_confidence[:10]:  # Show top 10
            value = prop.get('estimatedValue', 0)
            print(f"  • {prop.get('address')} - ${value:,.0f} ({prop.get('potential_llc')})")
        
        print(f"\\nMedium Confidence Matches: {len(medium_confidence)}")
        for prop in medium_confidence[:10]:  # Show top 10
            value = prop.get('estimatedValue', 0)
            print(f"  • {prop.get('address')} - ${value:,.0f} ({prop.get('potential_llc')})")
    
    # Investment Strategy Analysis
    print("\\n💡 LLC INVESTMENT STRATEGY ANALYSIS:")
    print("-" * 50)
    
    print("\\nTOM'S STRATEGY (NC ILM PROPERTIES LLC):")
    print("  • Focus: Wilmington, NC commercial district")
    print("  • Property Type: High-value commercial/multi-family")
    print("  • Investment Size: $1M+ per property")
    print("  • Strategy: Portfolio acquisition (same-day purchases)")
    
    print("\\nDUSTIN'S STRATEGY (Multiple LLCs):")
    dustin_llcs = [llc for llc in llc_data.keys() if llc != "NC ILM PROPERTIES LLC"]
    print(f"  • LLC Diversification: {len(dustin_llcs)} different LLCs")
    print("  • Geographic Focus: New Bern, NC + Coastal NC")
    print("  • Property Mix: Residential rentals + Commercial")
    print("  • Investment Range: $100K - $1.7M per property")
    
    # Recommendations
    print("\\n🎯 RECOMMENDATIONS FOR FINDING MORE LLC PROPERTIES:")
    print("-" * 50)
    print("1. Search for properties with similar naming patterns:")
    print("   • '[STATE] [CITY] PROPERTIES LLC'")
    print("   • '[ADJECTIVE] [NOUN] LLC' (SO EASY, GAGE PROPERTIES)")
    print("   • '[LOCATION] [TYPE] HOLDINGS LLC'")
    
    print("\\n2. Geographic clustering analysis:")
    print("   • More properties in Wilmington downtown district")
    print("   • Additional New Bern residential properties")
    print("   • Coastal NC vacation rental properties")
    
    print("\\n3. Purchase pattern analysis:")
    print("   • Look for same-day multiple property purchases")
    print("   • Properties with similar loan amounts/structures")
    print("   • Absentee owner properties in target areas")
    
    return {
        'total_known_properties': total_known_properties,
        'total_known_value': total_known_value,
        'potential_properties_count': len(potential_properties),
        'llc_data': llc_data,
        'potential_properties': potential_properties
    }

def main():
    """Main LLC discovery analysis"""
    
    print("STARTING LLC PROPERTY DISCOVERY ANALYSIS")
    print("="*60)
    
    # Step 1: Analyze known LLC properties
    llc_data = analyze_llc_ownership_patterns()
    
    # Step 2: Search for additional properties
    potential_properties = search_for_additional_llc_properties()
    
    # Step 3: Generate comprehensive report
    report_data = generate_llc_discovery_report(llc_data, potential_properties)
    
    # Step 4: Save results
    with open('llc_discovery_results.json', 'w') as f:
        json.dump(report_data, f, indent=2, default=str)
    
    print(f"\\n✅ Analysis complete! Results saved to: llc_discovery_results.json")
    
    # Create CSV for easy analysis
    all_properties = []
    
    # Add confirmed LLC properties
    for llc_name, properties in llc_data.items():
        for prop in properties:
            prop['llc_name'] = llc_name
            prop['status'] = 'CONFIRMED'
            all_properties.append(prop)
    
    # Add potential properties
    for prop in potential_properties:
        prop['llc_name'] = prop.get('potential_llc', 'UNKNOWN')
        prop['status'] = f"POTENTIAL_{prop.get('confidence', 'LOW')}"
        all_properties.append(prop)
    
    if all_properties:
        df = pd.DataFrame(all_properties)
        df.to_csv('llc_properties_analysis.csv', index=False)
        print(f"✅ Detailed data saved to: llc_properties_analysis.csv")

if __name__ == "__main__":
    main()
