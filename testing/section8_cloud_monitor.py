#!/usr/bin/env python3
"""
Section 8 Property Monitor - Cloud Version
Continuous monitoring for new Section 8 investment opportunities
Based on <PERSON> <PERSON> Dustin's successful investment patterns
"""

import requests
import json
import time
import os
import schedule
from datetime import datetime, timedelta
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import hashlib
import sqlite3
from pathlib import Path
import traceback

# API Configuration
REAL_ESTATE_API_KEY = "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
RESEND_API_KEY = "re_6HVDhgEB_RYCqL1cz248rFrUERt8g4Cyb"
EMAIL_TO = "<EMAIL>"  # Replace with your email

BASE_URL = "https://api.realestateapi.com"
RESEND_URL = "https://api.resend.com/emails"

headers = {
    "Content-Type": "application/json",
    "x-api-key": f"{REAL_ESTATE_API_KEY}"
}

resend_headers = {
    "Authorization": f"Bearer {RESEND_API_KEY}",
    "Content-Type": "application/json"
}

# Section 8 Investment Criteria (Based on Tom & Dustin Analysis)
SECTION8_CRITERIA = {
    # Geographic targets (expandable)
    "target_markets": [
        {"city": "NEW BERN", "state": "NC", "priority": "HIGH"},
        {"city": "WILMINGTON", "state": "NC", "priority": "HIGH"},
        {"city": "JACKSONVILLE", "state": "NC", "priority": "MEDIUM"},
        {"city": "GREENVILLE", "state": "NC", "priority": "MEDIUM"},
        {"city": "ROCKY MOUNT", "state": "NC", "priority": "MEDIUM"},
        {"city": "WILSON", "state": "NC", "priority": "MEDIUM"},
        {"city": "GOLDSBORO", "state": "NC", "priority": "MEDIUM"},
        {"city": "FAYETTEVILLE", "state": "NC", "priority": "LOW"},
    ],
    
    # Price ranges based on Dustin's successful Section 8 properties
    "price_ranges": {
        "PRIMARY": {"min": 80000, "max": 200000},    # Sweet spot
        "SECONDARY": {"min": 200000, "max": 300000}, # Higher end
        "OPPORTUNITY": {"min": 50000, "max": 80000}  # Distressed
    },
    
    # Property characteristics
    "property_specs": {
        "bedrooms_min": 2,
        "bedrooms_max": 4,
        "bathrooms_min": 1,
        "square_feet_min": 800,
        "square_feet_max": 2500,
        "year_built_min": 1970,  # Not too old for Section 8 standards
        "property_types": ["SINGLE_FAMILY", "MULTI_FAMILY", "TOWNHOUSE"]
    },
    
    # Investment criteria
    "investment_filters": {
        "absentee_owner": True,    # Current landlords
        "owner_occupied": False,   # Not owner-occupied
        "foreclosure": True,       # Opportunity properties
        "vacant": True            # Ready for rehab/rent
    }
}

# Fair Market Rent thresholds for cash flow analysis
FMR_MINIMUMS = {
    2: 800,   # 2BR minimum FMR
    3: 1000,  # 3BR minimum FMR  
    4: 1200   # 4BR minimum FMR
}

class Section8Monitor:
    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.setup_directories()
        self.seen_properties = set()
        self.daily_count = 0
        self.start_time = datetime.now()
        
    def setup_logging(self):
        """Configure logging for cloud deployment"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('section8_monitor.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_database(self):
        """Setup SQLite database for tracking properties"""
        self.db_path = 'section8_properties.db'
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monitored_properties (
                id INTEGER PRIMARY KEY,
                property_id TEXT UNIQUE,
                address TEXT,
                city TEXT,
                state TEXT,
                zip TEXT,
                price INTEGER,
                bedrooms INTEGER,
                bathrooms REAL,
                square_feet INTEGER,
                estimated_rent INTEGER,
                cash_flow INTEGER,
                roi_percentage REAL,
                first_seen TIMESTAMP,
                last_updated TIMESTAMP,
                status TEXT,
                priority TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_stats (
                date TEXT PRIMARY KEY,
                properties_found INTEGER,
                high_priority INTEGER,
                medium_priority INTEGER,
                low_priority INTEGER,
                avg_price REAL,
                avg_roi REAL
            )
        ''')
        
        conn.commit()
        conn.close()
        
    def setup_directories(self):
        """Create directory structure for daily reports"""
        self.base_dir = Path("section8_reports")
        self.base_dir.mkdir(exist_ok=True)
        
        today = datetime.now().strftime("%Y-%m-%d")
        self.today_dir = self.base_dir / today
        self.today_dir.mkdir(exist_ok=True)
        
    def search_section8_properties(self, market, price_range="PRIMARY"):
        """Search for Section 8 eligible properties in target market"""
        url = f"{BASE_URL}/v2/PropertySearch"
        
        criteria = SECTION8_CRITERIA
        price_config = criteria["price_ranges"][price_range]
        
        payload = {
            "city": market["city"],
            "state": market["state"],
            "estimated_value_min": price_config["min"],
            "estimated_value_max": price_config["max"],
            "bedrooms_min": criteria["property_specs"]["bedrooms_min"],
            "bedrooms_max": criteria["property_specs"]["bedrooms_max"],
            "bathrooms_min": criteria["property_specs"]["bathrooms_min"],
            "square_feet_min": criteria["property_specs"]["square_feet_min"],
            "square_feet_max": criteria["property_specs"]["square_feet_max"],
            "year_built_min": criteria["property_specs"]["year_built_min"],
            "property_type": criteria["property_specs"]["property_types"],
            "absentee_owner": criteria["investment_filters"]["absentee_owner"],
            "limit": 50,
            "sort": "estimated_value",
            "sort_direction": "asc"
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('data', [])
            else:
                self.logger.error(f"API Error {response.status_code}: {response.text}")
                return []
                
        except Exception as e:
            self.logger.error(f"Search error for {market['city']}: {str(e)}")
            return []
    
    def get_property_details(self, property_id):
        """Get detailed property information including demographics"""
        url = f"{BASE_URL}/v2/PropertyDetail"
        payload = {"id": property_id}
        
        try:
            response = requests.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Detail error for property {property_id}: {str(e)}")
            return None
    
    def calculate_section8_viability(self, property_data):
        """Calculate Section 8 investment viability score"""
        score = 0
        reasons = []
        
        # Basic property info
        bedrooms = property_data.get('bedrooms', 0)
        estimated_value = property_data.get('estimatedValue', 0)
        square_feet = property_data.get('squareFeet', 0)
        
        # Get detailed property info if available
        details = None
        if property_data.get('id'):
            details = self.get_property_details(property_data['id'])
            time.sleep(1)  # Rate limiting
        
        # Fair Market Rent analysis
        fair_market_rent = 0
        if details and details.get('demographics', {}).get('fairMarketRent'):
            fmr_data = details['demographics']['fairMarketRent']
            if bedrooms == 2:
                fair_market_rent = fmr_data.get('twoBedroom', 0)
            elif bedrooms == 3:
                fair_market_rent = fmr_data.get('threeBedroom', 0)
            elif bedrooms == 4:
                fair_market_rent = fmr_data.get('fourBedroom', 0)
        
        # Estimate rent if FMR not available
        if fair_market_rent == 0:
            # Conservative rent estimates based on area and bedrooms
            rent_per_sqft = 0.8  # Conservative estimate
            fair_market_rent = max(square_feet * rent_per_sqft, FMR_MINIMUMS.get(bedrooms, 600))
        
        # Score based on 1% rule and cash flow potential
        monthly_payment_estimate = estimated_value * 0.006  # 6% of value (taxes, insurance, etc.)
        gross_cash_flow = fair_market_rent - monthly_payment_estimate
        
        # Investment scoring
        if estimated_value > 0:
            roi_percentage = (gross_cash_flow * 12) / estimated_value * 100
            
            if roi_percentage > 15:
                score += 30
                reasons.append(f"Excellent ROI: {roi_percentage:.1f}%")
            elif roi_percentage > 10:
                score += 20
                reasons.append(f"Good ROI: {roi_percentage:.1f}%")
            elif roi_percentage > 5:
                score += 10
                reasons.append(f"Fair ROI: {roi_percentage:.1f}%")
        
        # Price range scoring (Dustin's sweet spot)
        if 80000 <= estimated_value <= 150000:
            score += 25
            reasons.append("Optimal price range for Section 8")
        elif 150000 <= estimated_value <= 200000:
            score += 15
            reasons.append("Good price range")
        
        # Bedroom count scoring
        if bedrooms == 3:
            score += 20
            reasons.append("3BR - highest Section 8 demand")
        elif bedrooms == 2:
            score += 15
            reasons.append("2BR - good Section 8 demand")
        elif bedrooms == 4:
            score += 10
            reasons.append("4BR - family Section 8 demand")
        
        # Property characteristics
        if property_data.get('absenteeOwner'):
            score += 10
            reasons.append("Absentee owner - potential motivated seller")
        
        if property_data.get('vacant'):
            score += 5
            reasons.append("Vacant - ready for rehab")
        
        year_built = property_data.get('yearBuilt', 0)
        if year_built >= 1980:
            score += 10
            reasons.append("Built 1980+ - meets Section 8 standards")
        elif year_built >= 1970:
            score += 5
            reasons.append("Built 1970+ - likely Section 8 eligible")
        
        # HUD designation bonus
        if details and details.get('demographics', {}).get('hudDesignations'):
            score += 15
            reasons.append("HUD designated area")
        
        return {
            'score': score,
            'reasons': reasons,
            'estimated_rent': fair_market_rent,
            'estimated_cash_flow': gross_cash_flow,
            'roi_percentage': roi_percentage if 'roi_percentage' in locals() else 0,
            'investment_priority': self.get_priority_level(score)
        }
    
    def get_priority_level(self, score):
        """Determine investment priority based on score"""
        if score >= 70:
            return "HIGH"
        elif score >= 50:
            return "MEDIUM"
        elif score >= 30:
            return "LOW"
        else:
            return "SKIP"
    
    def save_property_to_db(self, property_data, analysis):
        """Save property to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO monitored_properties 
            (property_id, address, city, state, zip, price, bedrooms, bathrooms, 
             square_feet, estimated_rent, cash_flow, roi_percentage, 
             first_seen, last_updated, status, priority)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            str(property_data.get('id', '')),
            property_data.get('address', ''),
            property_data.get('city', ''),
            property_data.get('state', ''),
            property_data.get('zip', ''),
            property_data.get('estimatedValue', 0),
            property_data.get('bedrooms', 0),
            property_data.get('bathrooms', 0),
            property_data.get('squareFeet', 0),
            analysis['estimated_rent'],
            analysis['estimated_cash_flow'],
            analysis['roi_percentage'],
            datetime.now(),
            datetime.now(),
            'NEW',
            analysis['investment_priority']
        ))
        
        conn.commit()
        conn.close()
    
    def save_daily_report(self, properties):
        """Save daily report to text file"""
        today = datetime.now().strftime("%Y-%m-%d")
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        report_file = self.today_dir / f"section8_opportunities_{today}.txt"
        
        # Separate properties by priority
        high_priority = [p for p in properties if p['analysis']['investment_priority'] == 'HIGH']
        medium_priority = [p for p in properties if p['analysis']['investment_priority'] == 'MEDIUM']
        low_priority = [p for p in properties if p['analysis']['investment_priority'] == 'LOW']
        
        report_content = f"""
SECTION 8 PROPERTY OPPORTUNITIES - {today} {timestamp}
================================================================

SUMMARY:
--------
Total Properties Found: {len(properties)}
High Priority: {len(high_priority)}
Medium Priority: {len(medium_priority)}
Low Priority: {len(low_priority)}

HIGH PRIORITY OPPORTUNITIES (Score 70+):
========================================
"""
        
        for i, prop in enumerate(high_priority, 1):
            p = prop['property']
            a = prop['analysis']
            
            report_content += f"""
{i}. {p.get('address', 'Unknown Address')}
   Price: ${p.get('estimatedValue', 0):,}
   Bedrooms: {p.get('bedrooms', 0)} | Bathrooms: {p.get('bathrooms', 0)}
   Square Feet: {p.get('squareFeet', 0):,}
   Estimated Rent: ${a['estimated_rent']:,.0f}/month
   Estimated Cash Flow: ${a['estimated_cash_flow']:,.0f}/month
   ROI: {a['roi_percentage']:.1f}%
   Score: {a['score']}/100
   
   Why This Property:
   {chr(10).join(f'   • {reason}' for reason in a['reasons'])}
   
   Property Details:
   • City: {p.get('city', '')}, {p.get('state', '')} {p.get('zip', '')}
   • Year Built: {p.get('yearBuilt', 'Unknown')}
   • Property Type: {p.get('propertyType', 'Unknown')}
   • Absentee Owner: {'Yes' if p.get('absenteeOwner') else 'No'}
   • Vacant: {'Yes' if p.get('vacant') else 'No'}
{'-' * 80}
"""
        
        report_content += f"""

MEDIUM PRIORITY OPPORTUNITIES (Score 50-69):
============================================
"""
        
        for i, prop in enumerate(medium_priority, 1):
            p = prop['property']
            a = prop['analysis']
            
            report_content += f"""
{i}. {p.get('address', 'Unknown Address')} - ${p.get('estimatedValue', 0):,}
   {p.get('bedrooms', 0)}BR/{p.get('bathrooms', 0)}BA | Est. Rent: ${a['estimated_rent']:,.0f} | ROI: {a['roi_percentage']:.1f}% | Score: {a['score']}
   Key Points: {', '.join(a['reasons'][:3])}
"""
        
        report_content += f"""

SEARCH CRITERIA USED:
=====================
Geographic Markets: {', '.join([f"{m['city']}, {m['state']}" for m in SECTION8_CRITERIA['target_markets'][:5]])}
Price Range: ${SECTION8_CRITERIA['price_ranges']['PRIMARY']['min']:,} - ${SECTION8_CRITERIA['price_ranges']['PRIMARY']['max']:,}
Bedrooms: {SECTION8_CRITERIA['property_specs']['bedrooms_min']}-{SECTION8_CRITERIA['property_specs']['bedrooms_max']}
Property Types: {', '.join(SECTION8_CRITERIA['property_specs']['property_types'])}

INVESTMENT STRATEGY (Based on Dustin & Tom's Success):
======================================================
• Focus on properties under $200K for optimal Section 8 returns
• Target 2-4 bedroom properties (highest demand)
• Look for absentee owners (motivated sellers)
• Prioritize properties built after 1970 (Section 8 eligible)
• Ensure positive cash flow with Fair Market Rent rates
• Geographic clustering for management efficiency

Next Update: {(datetime.now() + timedelta(minutes=1)).strftime('%H:%M:%S')}
"""
        
        # Write to file
        with open(report_file, 'w') as f:
            f.write(report_content)
        
        self.logger.info(f"Daily report saved: {len(properties)} properties, {len(high_priority)} high priority")
        
        return report_file
    
    def send_email_alert(self, properties, report_file):
        """Send email alert for new high-priority properties"""
        high_priority = [p for p in properties if p['analysis']['investment_priority'] == 'HIGH']
        
        if not high_priority:
            return  # No high priority properties to report
        
        subject = f"🏠 {len(high_priority)} New Section 8 Investment Opportunities Found!"
        
        # Create email body
        email_body = f"""
<h2>Section 8 Property Alert - {datetime.now().strftime('%Y-%m-%d %H:%M')}</h2>

<h3>🎯 High Priority Opportunities ({len(high_priority)} found)</h3>
"""
        
        for i, prop in enumerate(high_priority[:5], 1):  # Show top 5 in email
            p = prop['property']
            a = prop['analysis']
            
            email_body += f"""
<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;">
    <h4>{i}. {p.get('address', 'Unknown Address')}</h4>
    <p><strong>Price:</strong> ${p.get('estimatedValue', 0):,} | <strong>Score:</strong> {a['score']}/100</p>
    <p><strong>Specs:</strong> {p.get('bedrooms', 0)}BR/{p.get('bathrooms', 0)}BA | {p.get('squareFeet', 0):,} sq ft</p>
    <p><strong>Financials:</strong> Est. Rent ${a['estimated_rent']:,.0f}/mo | Cash Flow ${a['estimated_cash_flow']:,.0f}/mo | ROI {a['roi_percentage']:.1f}%</p>
    <p><strong>Why This Property:</strong></p>
    <ul>
"""
            
            for reason in a['reasons'][:4]:  # Top 4 reasons
                email_body += f"<li>{reason}</li>"
            
            email_body += """
    </ul>
</div>
"""
        
        email_body += f"""
<p><strong>Total Properties Analyzed Today:</strong> {len(properties)}</p>
<p><strong>Report Location:</strong> {report_file}</p>

<h3>📊 Investment Criteria (Based on Successful Investors)</h3>
<ul>
    <li>Price Range: $80K-$200K (optimal Section 8 range)</li>
    <li>Target: 2-4 bedroom properties</li>
    <li>Focus: Absentee owner properties (motivated sellers)</li>
    <li>Goal: Positive cash flow with Fair Market Rent</li>
</ul>

<p><em>This alert was generated by the Section 8 Property Monitor based on analysis of successful investor patterns.</em></p>
"""
        
        # Send via Resend API
        payload = {
            "from": "Section8Monitor <<EMAIL>>",  # Replace with your domain
            "to": [EMAIL_TO],
            "subject": subject,
            "html": email_body
        }
        
        try:
            response = requests.post(RESEND_URL, headers=resend_headers, json=payload)
            
            if response.status_code == 200:
                self.logger.info(f"Email alert sent: {len(high_priority)} high priority properties")
            else:
                self.logger.error(f"Email error {response.status_code}: {response.text}")
                
        except Exception as e:
            self.logger.error(f"Email exception: {str(e)}")
    
    def run_search_cycle(self):
        """Run one complete search cycle"""
        self.logger.info("Starting search cycle...")
        
        all_properties = []
        new_properties = []
        
        # Search each target market
        for market in SECTION8_CRITERIA["target_markets"]:
            self.logger.info(f"Searching {market['city']}, {market['state']}...")
            
            # Search primary price range
            properties = self.search_section8_properties(market, "PRIMARY")
            
            for prop in properties:
                prop_id = str(prop.get('id', ''))
                prop_hash = hashlib.md5(f"{prop.get('address', '')}{prop.get('estimatedValue', 0)}".encode()).hexdigest()
                
                # Skip if we've seen this property recently
                if prop_hash in self.seen_properties:
                    continue
                
                # Analyze Section 8 viability
                analysis = self.calculate_section8_viability(prop)
                
                # Only save/report properties with decent scores
                if analysis['investment_priority'] != 'SKIP':
                    property_with_analysis = {
                        'property': prop,
                        'analysis': analysis,
                        'found_at': datetime.now().isoformat()
                    }
                    
                    all_properties.append(property_with_analysis)
                    
                    # Save to database
                    self.save_property_to_db(prop, analysis)
                    
                    # Track as new if high priority
                    if analysis['investment_priority'] in ['HIGH', 'MEDIUM']:
                        new_properties.append(property_with_analysis)
                        self.logger.info(f"Found {analysis['investment_priority']} priority: {prop.get('address', '')} - ${prop.get('estimatedValue', 0):,}")
                
                self.seen_properties.add(prop_hash)
            
            time.sleep(2)  # Rate limiting between markets
        
        # Save daily report
        if all_properties:
            report_file = self.save_daily_report(all_properties)
            
            # Send email for high priority properties
            high_priority_new = [p for p in new_properties if p['analysis']['investment_priority'] == 'HIGH']
            if high_priority_new:
                self.send_email_alert(new_properties, report_file)
        
        self.daily_count += len(new_properties)
        self.logger.info(f"Search cycle complete: {len(new_properties)} new properties, {self.daily_count} total today")
    
    def run_monitor(self):
        """Main monitoring loop"""
        self.logger.info("Starting Section 8 Property Monitor...")
        self.logger.info(f"Monitoring {len(SECTION8_CRITERIA['target_markets'])} markets")
        self.logger.info(f"Email alerts: {EMAIL_TO}")
        
        # Schedule the search to run every minute
        schedule.every(1).minutes.do(self.run_search_cycle)
        
        # Run initial search
        self.run_search_cycle()
        
        # Keep running
        while True:
            try:
                schedule.run_pending()
                time.sleep(30)  # Check every 30 seconds
                
                # Reset daily count at midnight
                if datetime.now().hour == 0 and datetime.now().minute == 0:
                    self.daily_count = 0
                    self.seen_properties.clear()
                    self.setup_directories()  # Create new daily folder
                    
            except KeyboardInterrupt:
                self.logger.info("Monitor stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Monitor error: {str(e)}")
                self.logger.error(traceback.format_exc())
                time.sleep(60)  # Wait before retrying

def main():
    """Main function"""
    print("Section 8 Property Monitor - Cloud Edition")
    print("="*50)
    print("Based on Tom & Dustin's successful investment patterns")
    print(f"Monitoring {len(SECTION8_CRITERIA['target_markets'])} markets every minute")
    print(f"Email alerts to: {EMAIL_TO}")
    print("="*50)
    
    monitor = Section8Monitor()
    monitor.run_monitor()

if __name__ == "__main__":
    main()
