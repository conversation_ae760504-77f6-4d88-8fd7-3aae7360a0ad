#!/usr/bin/env python3
"""
Targeted LLC Property Search
Use identified patterns to find hidden LLC properties via API
"""

import requests
import json
import time
from datetime import datetime

# API Configuration
API_KEY = "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
BASE_URL = "https://api.realestateapi.com"

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {API_KEY}"
}

# Search targets based on pattern analysis
tom_search_targets = {
    "geographic_focus": [
        {"city": "WILMINGTON", "state": "NC", "zip": "28401"},
        {"city": "WILMINGTON", "state": "NC", "zip": "28412"},
        {"city": "WILMINGTON", "state": "NC", "zip": "28405"},
        {"city": "WILMINGTON", "state": "NC", "zip": "28409"}
    ],
    "value_range": {"min": 500000, "max": 3000000},
    "property_types": ["MULTI_FAMILY", "COMMERCIAL"],
    "characteristics": {
        "absentee_owner": True,
        "high_value": True
    }
}

dustin_search_targets = {
    "geographic_focus": [
        {"city": "NEW BERN", "state": "NC", "zip": "28560"},
        {"city": "NEW BERN", "state": "NC", "zip": "28562"},
        {"city": "ATLANTIC BEACH", "state": "NC", "zip": "28512"},
        {"city": "TRENT WOODS", "state": "NC", "zip": "28562"}
    ],
    "value_range": {"min": 200000, "max": 2000000},
    "property_types": ["SINGLE_FAMILY", "MULTI_FAMILY"],
    "characteristics": {
        "absentee_owner": True,
        "rental_property": True
    }
}

def search_properties_by_criteria(search_params):
    """Search for properties using Property Search API"""
    url = f"{BASE_URL}/v2/PropertySearch"
    
    try:
        response = requests.post(url, headers=headers, json=search_params)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Search error: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"Search exception: {str(e)}")
        return None

def get_property_detail_with_owner(property_id):
    """Get detailed property info including owner details"""
    url = f"{BASE_URL}/v2/PropertyDetail"
    
    payload = {"id": property_id}
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            return response.json()
        else:
            return None
            
    except Exception as e:
        return None

def analyze_property_for_llc_ownership(property_data):
    """Analyze property characteristics to identify potential LLC ownership"""
    
    llc_indicators = {
        'owner_name_llc': False,
        'absentee_owner': False,
        'high_value': False,
        'commercial_zoning': False,
        'multiple_units': False,
        'cash_purchase': False,
        'suspicious_score': 0
    }
    
    # Check owner information
    owner_info = property_data.get('ownerInfo', {})
    owner_name = f"{owner_info.get('owner1FirstName', '')} {owner_info.get('owner1LastName', '')}".strip()
    
    # LLC indicators in owner name
    llc_keywords = ['LLC', 'PROPERTIES', 'HOLDINGS', 'MANAGEMENT', 'INVESTMENT', 'REAL ESTATE', 'GROUP']
    if any(keyword in owner_name.upper() for keyword in llc_keywords):
        llc_indicators['owner_name_llc'] = True
        llc_indicators['suspicious_score'] += 3
    
    # Absentee owner
    if property_data.get('absenteeOwner'):
        llc_indicators['absentee_owner'] = True
        llc_indicators['suspicious_score'] += 2
    
    # High value property
    estimated_value = property_data.get('estimatedValue', 0)
    if estimated_value > 500000:
        llc_indicators['high_value'] = True
        llc_indicators['suspicious_score'] += 1
    
    # Multiple units (commercial/investment property)
    units = property_data.get('propertyInfo', {}).get('units', 1)
    if units and units > 1:
        llc_indicators['multiple_units'] = True
        llc_indicators['suspicious_score'] += 2
    
    return llc_indicators

def search_tom_pattern_properties():
    """Search for properties matching Tom's LLC patterns"""
    
    print("🔍 SEARCHING FOR TOM'S HIDDEN LLC PROPERTIES")
    print("="*60)
    
    tom_candidates = []
    
    for area in tom_search_targets["geographic_focus"]:
        print(f"\\nSearching {area['city']}, {area['state']} {area.get('zip', '')}")
        
        search_params = {
            "city": area["city"],
            "state": area["state"],
            "estimated_value_min": tom_search_targets["value_range"]["min"],
            "estimated_value_max": tom_search_targets["value_range"]["max"],
            "absentee_owner": True,
            "limit": 50
        }
        
        if "zip" in area:
            search_params["zip"] = area["zip"]
        
        results = search_properties_by_criteria(search_params)
        
        if results and 'data' in results:
            properties = results['data']
            print(f"  Found {len(properties)} high-value absentee owner properties")
            
            for prop in properties[:10]:  # Analyze top 10 per area
                property_id = prop.get('id')
                if property_id:
                    # Get detailed information
                    details = get_property_detail_with_owner(property_id)
                    
                    if details:
                        llc_analysis = analyze_property_for_llc_ownership(details)
                        
                        if llc_analysis['suspicious_score'] >= 3:
                            owner_info = details.get('ownerInfo', {})
                            owner_name = f"{owner_info.get('owner1FirstName', '')} {owner_info.get('owner1LastName', '')}".strip()
                            
                            candidate = {
                                'address': prop.get('address'),
                                'estimated_value': prop.get('estimatedValue'),
                                'owner_name': owner_name,
                                'suspicious_score': llc_analysis['suspicious_score'],
                                'indicators': llc_analysis,
                                'category': 'TOM_CANDIDATE'
                            }
                            
                            tom_candidates.append(candidate)
                            
                            print(f"    🎯 CANDIDATE: {prop.get('address')}")
                            print(f"       Owner: {owner_name}")
                            print(f"       Value: ${prop.get('estimatedValue', 0):,}")
                            print(f"       Score: {llc_analysis['suspicious_score']}")
                
                time.sleep(0.5)  # Rate limiting
        
        time.sleep(2)  # Rate limiting between areas
    
    return tom_candidates

def search_dustin_pattern_properties():
    """Search for properties matching Dustin's LLC patterns"""
    
    print("\\n🔍 SEARCHING FOR DUSTIN'S HIDDEN LLC PROPERTIES")
    print("="*60)
    
    dustin_candidates = []
    
    for area in dustin_search_targets["geographic_focus"]:
        print(f"\\nSearching {area['city']}, {area['state']} {area.get('zip', '')}")
        
        search_params = {
            "city": area["city"],
            "state": area["state"],
            "estimated_value_min": dustin_search_targets["value_range"]["min"],
            "estimated_value_max": dustin_search_targets["value_range"]["max"],
            "absentee_owner": True,
            "limit": 50
        }
        
        if "zip" in area:
            search_params["zip"] = area["zip"]
        
        results = search_properties_by_criteria(search_params)
        
        if results and 'data' in results:
            properties = results['data']
            print(f"  Found {len(properties)} potential rental properties")
            
            for prop in properties[:15]:  # Analyze top 15 per area
                property_id = prop.get('id')
                if property_id:
                    # Get detailed information
                    details = get_property_detail_with_owner(property_id)
                    
                    if details:
                        llc_analysis = analyze_property_for_llc_ownership(details)
                        
                        if llc_analysis['suspicious_score'] >= 2:  # Lower threshold for Dustin
                            owner_info = details.get('ownerInfo', {})
                            owner_name = f"{owner_info.get('owner1FirstName', '')} {owner_info.get('owner1LastName', '')}".strip()
                            
                            candidate = {
                                'address': prop.get('address'),
                                'estimated_value': prop.get('estimatedValue'),
                                'owner_name': owner_name,
                                'suspicious_score': llc_analysis['suspicious_score'],
                                'indicators': llc_analysis,
                                'category': 'DUSTIN_CANDIDATE'
                            }
                            
                            dustin_candidates.append(candidate)
                            
                            print(f"    🎯 CANDIDATE: {prop.get('address')}")
                            print(f"       Owner: {owner_name}")
                            print(f"       Value: ${prop.get('estimatedValue', 0):,}")
                            print(f"       Score: {llc_analysis['suspicious_score']}")
                
                time.sleep(0.5)  # Rate limiting
        
        time.sleep(2)  # Rate limiting between areas
    
    return dustin_candidates

def cross_reference_known_patterns(candidates):
    """Cross-reference candidates against known LLC patterns"""
    
    print("\\n🔗 CROSS-REFERENCING AGAINST KNOWN PATTERNS")
    print("="*60)
    
    # Known Tom LLC patterns
    tom_llc_patterns = [
        'NC ILM PROPERTIES',
        'WILMINGTON',
        'ILM',
        'COASTAL PROPERTIES',
        'NC COASTAL'
    ]
    
    # Known Dustin LLC patterns  
    dustin_llc_patterns = [
        'EASY',
        'GAGE',
        'MANAGEMENT',
        'HOLDINGS',
        'NEUSE',
        'TRENT',
        'BIRDHOUSE',
        'FERN'
    ]
    
    high_confidence_matches = []
    
    for candidate in candidates:
        owner_name = candidate['owner_name'].upper()
        match_score = 0
        matched_patterns = []
        
        if candidate['category'] == 'TOM_CANDIDATE':
            for pattern in tom_llc_patterns:
                if pattern in owner_name:
                    match_score += 2
                    matched_patterns.append(pattern)
        
        elif candidate['category'] == 'DUSTIN_CANDIDATE':
            for pattern in dustin_llc_patterns:
                if pattern in owner_name:
                    match_score += 2
                    matched_patterns.append(pattern)
        
        if match_score > 0:
            candidate['pattern_match_score'] = match_score
            candidate['matched_patterns'] = matched_patterns
            candidate['total_confidence'] = candidate['suspicious_score'] + match_score
            high_confidence_matches.append(candidate)
            
            print(f"🎯 HIGH CONFIDENCE MATCH:")
            print(f"   Address: {candidate['address']}")
            print(f"   Owner: {candidate['owner_name']}")
            print(f"   Value: ${candidate['estimated_value']:,}")
            print(f"   Matched Patterns: {', '.join(matched_patterns)}")
            print(f"   Total Confidence: {candidate['total_confidence']}")
            print()
    
    return high_confidence_matches

def generate_llc_discovery_report(tom_candidates, dustin_candidates, high_confidence):
    """Generate comprehensive discovery report"""
    
    print("\\n" + "="*60)
    print("LLC PROPERTY DISCOVERY REPORT")
    print("="*60)
    
    print(f"\\n📊 DISCOVERY SUMMARY:")
    print(f"Tom Candidates Found: {len(tom_candidates)}")
    print(f"Dustin Candidates Found: {len(dustin_candidates)}")
    print(f"High Confidence Matches: {len(high_confidence)}")
    
    # Calculate potential hidden portfolio value
    tom_total = sum(c.get('estimated_value', 0) for c in tom_candidates)
    dustin_total = sum(c.get('estimated_value', 0) for c in dustin_candidates)
    
    print(f"\\n💰 ESTIMATED HIDDEN PORTFOLIO VALUE:")
    print(f"Tom's Additional Properties: ${tom_total:,}")
    print(f"Dustin's Additional Properties: ${dustin_total:,}")
    print(f"Combined Hidden Portfolio: ${tom_total + dustin_total:,}")
    
    # Top candidates by confidence
    print(f"\\n🎯 TOP CANDIDATES FOR INVESTIGATION:")
    
    all_candidates = tom_candidates + dustin_candidates
    top_candidates = sorted(all_candidates, key=lambda x: x.get('total_confidence', x['suspicious_score']), reverse=True)
    
    for i, candidate in enumerate(top_candidates[:10]):
        print(f"\\n{i+1}. {candidate['address']}")
        print(f"   Owner: {candidate['owner_name']}")
        print(f"   Value: ${candidate['estimated_value']:,}")
        print(f"   Confidence Score: {candidate.get('total_confidence', candidate['suspicious_score'])}")
        if 'matched_patterns' in candidate:
            print(f"   Matched Patterns: {', '.join(candidate['matched_patterns'])}")
    
    return {
        'tom_candidates': tom_candidates,
        'dustin_candidates': dustin_candidates,
        'high_confidence_matches': high_confidence,
        'total_candidates': len(tom_candidates) + len(dustin_candidates),
        'estimated_hidden_value': tom_total + dustin_total
    }

def main():
    """Main LLC discovery search"""
    
    print("TARGETED LLC PROPERTY DISCOVERY")
    print("="*60)
    print("Using Real Estate API to find hidden LLC properties")
    print("Based on identified ownership patterns\\n")
    
    # Search for Tom's hidden properties
    tom_candidates = search_tom_pattern_properties()
    
    # Search for Dustin's hidden properties  
    dustin_candidates = search_dustin_pattern_properties()
    
    # Cross-reference against known patterns
    all_candidates = tom_candidates + dustin_candidates
    high_confidence = cross_reference_known_patterns(all_candidates)
    
    # Generate comprehensive report
    report_data = generate_llc_discovery_report(tom_candidates, dustin_candidates, high_confidence)
    
    # Save results
    with open('targeted_llc_search_results.json', 'w') as f:
        json.dump(report_data, f, indent=2, default=str)
    
    print(f"\\n✅ Results saved to: targeted_llc_search_results.json")
    
    print(f"\\n🎯 NEXT STEPS:")
    print("1. Investigate high-confidence matches manually")
    print("2. Cross-reference with public records")
    print("3. Check property management companies")
    print("4. Analyze mailing addresses for patterns")
    print("5. Look up LLC registration records")

if __name__ == "__main__":
    main()
