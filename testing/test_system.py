#!/usr/bin/env python3
"""
Section 8 Monitor - Local Test
Test the monitoring system locally before cloud deployment
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from section8_cloud_monitor import Section8Monitor
import json

def test_configuration():
    """Test system configuration"""
    print("🔧 Testing Configuration...")
    
    # Check config file
    if not os.path.exists('config.json'):
        print("❌ config.json not found")
        return False
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Check required keys
        required_keys = ['api_keys', 'email_settings', 'target_markets']
        for key in required_keys:
            if key not in config:
                print(f"❌ Missing {key} in config.json")
                return False
        
        print("✅ Configuration file is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error reading config.json: {str(e)}")
        return False

def test_api_connection():
    """Test Real Estate API connection"""
    print("\\n🌐 Testing API Connection...")
    
    import requests
    
    API_KEY = "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
    BASE_URL = "https://api.realestateapi.com"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }
    
    # Test with a simple search
    payload = {
        "city": "NEW BERN",
        "state": "NC",
        "limit": 1
    }
    
    try:
        response = requests.post(f"{BASE_URL}/v2/PropertySearch", headers=headers, json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Real Estate API connection successful")
            print(f"   Sample data: {len(data.get('data', []))} properties returned")
            return True
        else:
            print(f"❌ API Error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API Connection Error: {str(e)}")
        return False

def test_email_service():
    """Test email service (Resend API)"""
    print("\\n📧 Testing Email Service...")
    
    import requests
    
    RESEND_API_KEY = "re_6HVDhgEB_RYCqL1cz248rFrUERt8g4Cyb"
    
    headers = {
        "Authorization": f"Bearer {RESEND_API_KEY}",
        "Content-Type": "application/json"
    }
    
    # Test email payload
    payload = {
        "from": "Section8Monitor <<EMAIL>>",
        "to": ["<EMAIL>"],
        "subject": "🏠 Section 8 Monitor Test Email",
        "html": """
        <h2>Section 8 Monitor Test</h2>
        <p>This is a test email from your Section 8 property monitoring system.</p>
        <p>If you receive this, your email alerts are working correctly!</p>
        <p><strong>System Status:</strong> ✅ Ready for deployment</p>
        """
    }
    
    try:
        response = requests.post("https://api.resend.com/emails", headers=headers, json=payload)
        
        if response.status_code == 200:
            print("✅ Email service test successful")
            print("   Check your email for test message")
            return True
        else:
            print(f"❌ Email Error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Email Service Error: {str(e)}")
        return False

def test_database_setup():
    """Test database setup and operations"""
    print("\\n🗄️ Testing Database Setup...")
    
    try:
        import sqlite3
        
        # Test database creation
        conn = sqlite3.connect('test_section8.db')
        cursor = conn.cursor()
        
        # Create test table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_properties (
                id INTEGER PRIMARY KEY,
                address TEXT,
                price INTEGER
            )
        ''')
        
        # Insert test data
        cursor.execute("INSERT INTO test_properties (address, price) VALUES (?, ?)", 
                      ("123 Test St, Test City, NC", 125000))
        
        # Query test data
        cursor.execute("SELECT * FROM test_properties")
        results = cursor.fetchall()
        
        conn.commit()
        conn.close()
        
        # Clean up test database
        os.remove('test_section8.db')
        
        print("✅ Database operations working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Database Error: {str(e)}")
        return False

def run_sample_search():
    """Run a sample property search"""
    print("\\n🔍 Running Sample Property Search...")
    
    try:
        monitor = Section8Monitor()
        
        # Run a single search cycle (modified for testing)
        print("   Initializing monitor...")
        
        # Test search for one market
        test_market = {"city": "NEW BERN", "state": "NC", "priority": "HIGH"}
        properties = monitor.search_section8_properties(test_market, "PRIMARY")
        
        if properties:
            print(f"✅ Sample search successful - found {len(properties)} properties")
            
            # Analyze first property
            if len(properties) > 0:
                analysis = monitor.calculate_section8_viability(properties[0])
                print(f"   Sample analysis: Score {analysis['score']}/100, Priority: {analysis['investment_priority']}")
            
            return True
        else:
            print("⚠️ No properties found in sample search (this is normal)")
            return True
            
    except Exception as e:
        print(f"❌ Sample Search Error: {str(e)}")
        return False

def run_full_test():
    """Run complete system test"""
    print("SECTION 8 MONITOR - SYSTEM TEST")
    print("="*50)
    print("Testing all system components before deployment...")
    
    tests = [
        ("Configuration", test_configuration),
        ("API Connection", test_api_connection),
        ("Email Service", test_email_service),
        ("Database Setup", test_database_setup),
        ("Sample Search", run_sample_search)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} Test Failed: {str(e)}")
    
    print("\\n" + "="*50)
    print("TEST RESULTS SUMMARY")
    print("="*50)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is ready for deployment.")
        print("\\n🚀 Next Steps:")
        print("1. Run: ./deploy.sh")
        print("2. Check dashboard at http://your-server:8080")
        print("3. Monitor email for first alerts")
        return True
    else:
        print("❌ Some tests failed. Please fix issues before deployment.")
        print("\\n🔧 Common Fixes:")
        print("• Check API keys in config.json")
        print("• Verify internet connection")
        print("• Ensure email address is correct")
        return False

if __name__ == "__main__":
    success = run_full_test()
    sys.exit(0 if success else 1)
