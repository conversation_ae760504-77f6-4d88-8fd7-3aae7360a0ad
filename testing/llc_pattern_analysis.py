#!/usr/bin/env python3
"""
Quick LLC Pattern Analysis
Identify patterns in known LLC properties to find hidden holdings
"""

import json
from datetime import datetime

# Tom's properties with detailed transaction data
tom_properties = [
    {"address": "106 SYDNEY HARBOR ST, CHAPEL HILL, NC 27516", "buyer": "CRUZ JANICE", "price": None, "date": "03/22/2025"},
    {"address": "353 IMAGINE WAY, PITTSBORO, NC 27312", "buyer": "CRUZ FERNANDO", "price": 714000, "date": "12/03/2024"},
    {"address": "1125 BELLE MEADE ISLAND DR, MIAMI, FL 33138", "buyer": "CRUZ THOMAS M", "price": 17750000, "date": "09/27/2023"},
    {"address": "809 INLET VIEW DR, WILMINGTON, NC 28409", "buyer": "PELON WILLIAM D", "price": 2400000, "date": "01/02/2024"},
    {"address": "6017 TARIN RD, WILMINGTON, NC 28409", "buyer": "ARTHUR BRIAN C JESSICA M", "price": 335000, "date": "06/02/2020"},
    {"address": "1109 FANNING ST, WILMINGTON, NC 28401", "buyer": "NC ILM PROPERTIES LLC", "price": 1775000, "date": "05/27/2022"},
    {"address": "317 QUEEN ST, WILMINGTON, NC 28401", "buyer": "NC ILM PROPERTIES LLC", "price": 1775000, "date": "05/27/2022"},
    {"address": "720 N 11TH ST, WILMINGTON, NC 28401", "buyer": "NC ILM PROPERTIES LLC", "price": 1775000, "date": "05/27/2022"},
    {"address": "2728 WORTH DR, WILMINGTON, NC 28412", "buyer": "NC ILM PROPERTIES LLC", "price": 1775000, "date": "05/27/2022"},
    {"address": "819 WOOSTER ST, WILMINGTON, NC 28401", "buyer": "NC ILM PROPERTIES LLC", "price": 1775000, "date": "05/27/2022"},
    {"address": "208 GORES ROW, WILMINGTON, NC 28401", "buyer": "WILKERSON JOHN ADELAIDE", "price": None, "date": "11/04/2022"},
    {"address": "3708 ANTELOPE TRL, WILMINGTON, NC 28409", "buyer": "KELLY PHILLIP A ASHLEY M", "price": 223000, "date": "09/13/2021"},
    {"address": "5904 CAMELOT CT, WILMINGTON, NC 28409", "buyer": "ANDERSON MARK", "price": 259000, "date": "12/21/2021"},
    {"address": "4615 MCCLELLAND DR UNIT 201, WILMINGTON, NC 28405", "buyer": "FERRI NICHOLAS R ASHLEY N", "price": 88000, "date": "06/13/2019"},
    {"address": "618 CONDO CLUB DR UNIT 308, WILMINGTON, NC 28412", "buyer": "LEITTER EDWARD Y SANDRA R", "price": 130500, "date": "02/27/2019"},
    {"address": "618 CONDO CLUB DR UNIT 104, WILMINGTON, NC 28412", "buyer": "ROSEMAN MICHAEL", "price": 115000, "date": "07/21/2015"}
]

# Dustin's properties with LLC connections identified
dustin_llc_connections = [
    {"address": "712 MADAM MOORES LN, NEW BERN, NC 28562", "buyer": "SO EASY LLC", "price": 1750000, "date": "10/30/2023"},
    {"address": "2400 NEUSE BLVD, NEW BERN, NC 28562", "buyer": "GAGE PROPERTIES LLC", "price": 675000, "date": "01/02/2023"},
    {"address": "1150 US 70 HWY E, NEW BERN, NC 28560", "buyer": "CAR MANAGEMENT LLC", "price": 600000, "date": "12/14/2017"},
    {"address": "903 SIMMONS ST, NEW BERN, NC 28560", "buyer": "BIRDHOUSE HOLDINGS LLC", "price": None, "date": "09/21/2020"},
    {"address": "301 COMMERCE WAY, ATLANTIC BEACH, NC 28512", "buyer": "FERN FOUNDATION LLC", "price": None, "date": "03/14/2025"},
    {"address": "3584 RED OAK DR, NEW BERN, NC 28562", "buyer": "NEUSE TRENT HOLDINGS I LLC", "price": None, "date": None}
]

def analyze_llc_patterns():
    """Analyze patterns in LLC ownership and transactions"""
    
    print("LLC OWNERSHIP PATTERN ANALYSIS")
    print("="*60)
    
    # Tom's NC ILM PROPERTIES LLC Analysis
    print("\\n🏢 TOM'S NC ILM PROPERTIES LLC PATTERN:")
    print("-" * 50)
    
    nc_ilm_properties = [p for p in tom_properties if p['buyer'] == 'NC ILM PROPERTIES LLC']
    
    print(f"Properties: {len(nc_ilm_properties)}")
    print(f"Purchase Date: {nc_ilm_properties[0]['date']} (ALL SAME DAY)")
    print(f"Total Investment: ${sum(p['price'] for p in nc_ilm_properties):,}")
    print(f"Average Price: ${sum(p['price'] for p in nc_ilm_properties) / len(nc_ilm_properties):,.0f}")
    
    print("\\nGeographic Analysis:")
    zip_codes = {}
    for prop in nc_ilm_properties:
        zip_code = prop['address'][-5:]
        zip_codes[zip_code] = zip_codes.get(zip_code, 0) + 1
    
    for zip_code, count in zip_codes.items():
        print(f"  ZIP {zip_code}: {count} properties")
    
    print("\\nStreet Analysis (potential commercial district):")
    for prop in nc_ilm_properties:
        street = prop['address'].split(',')[0]
        print(f"  • {street}")
    
    # Dustin's LLC Network Analysis
    print("\\n🏗️ DUSTIN'S LLC NETWORK PATTERN:")
    print("-" * 50)
    
    print(f"Number of Different LLCs: {len(dustin_llc_connections)}")
    print(f"Geographic Focus: New Bern, NC + Coastal areas")
    
    dustin_llcs_with_prices = [p for p in dustin_llc_connections if p['price']]
    if dustin_llcs_with_prices:
        total_value = sum(p['price'] for p in dustin_llcs_with_prices)
        print(f"Total Known Investment: ${total_value:,}")
        print(f"Average Property Value: ${total_value / len(dustin_llcs_with_prices):,.0f}")
    
    print("\\nLLC Naming Patterns:")
    for prop in dustin_llc_connections:
        llc_name = prop['buyer']
        print(f"  • {llc_name}")
    
    # Identify suspicious patterns that might indicate more properties
    print("\\n🔍 PATTERNS TO INVESTIGATE:")
    print("-" * 50)
    
    print("\\n1. TOM'S POTENTIAL HIDDEN PROPERTIES:")
    print("   • More properties in Wilmington ZIP 28401, 28412")
    print("   • Properties purchased by other buyers on 05/27/2022 (same day)")
    print("   • Properties with exactly $1,775,000 purchase price")
    print("   • Look for 'NC [CITY] PROPERTIES LLC' variations")
    
    # Check for other suspicious buyers in Tom's list
    tom_other_buyers = [p for p in tom_properties if p['buyer'] != 'NC ILM PROPERTIES LLC' and p['buyer'] != 'CRUZ THOMAS M' and 'CRUZ' not in p['buyer']]
    
    print("\\n   Suspicious third-party buyers (possible nominees):")
    for prop in tom_other_buyers:
        if prop['price'] and prop['price'] > 200000:  # Focus on higher-value properties
            print(f"     • {prop['buyer']} - {prop['address']} (${prop['price']:,})")
    
    print("\\n2. DUSTIN'S POTENTIAL HIDDEN PROPERTIES:")
    print("   • More LLCs with similar naming patterns:")
    print("     - '[ADJECTIVE] [NOUN] LLC' (SO EASY, GAGE PROPERTIES)")
    print("     - '[LOCATION] [TYPE] HOLDINGS LLC' (NEUSE TRENT HOLDINGS)")
    print("     - '[INDUSTRY] [WORD] LLC' (CAR MANAGEMENT)")
    print("     - '[NOUN] HOLDINGS LLC' (BIRDHOUSE HOLDINGS)")
    
    print("\\n   Geographic clustering suggests more properties in:")
    dustin_cities = {}
    for prop in dustin_llc_connections:
        city = prop['address'].split(',')[1].strip()
        dustin_cities[city] = dustin_cities.get(city, 0) + 1
    
    for city, count in dustin_cities.items():
        print(f"     • {city}: {count} known LLC properties")
    
    # Family name analysis
    print("\\n3. FAMILY MEMBER NOMINEE PURCHASES:")
    print("   • Tom's family members as buyers:")
    
    tom_family_buyers = [p for p in tom_properties if 'CRUZ' in p['buyer']]
    for prop in tom_family_buyers:
        if prop['buyer'] != 'CRUZ THOMAS M':
            print(f"     • {prop['buyer']} - {prop['address']}")
    
    return {
        'tom_llc_properties': nc_ilm_properties,
        'dustin_llc_properties': dustin_llc_connections,
        'tom_other_buyers': tom_other_buyers,
        'tom_family_buyers': tom_family_buyers
    }

def generate_search_targets():
    """Generate specific targets for API searches"""
    
    print("\\n" + "="*60)
    print("RECOMMENDED SEARCH TARGETS")
    print("="*60)
    
    print("\\n📍 GEOGRAPHIC SEARCH TARGETS:")
    print("-" * 40)
    print("• Wilmington, NC ZIP 28401 (Tom's commercial district)")
    print("• Wilmington, NC ZIP 28412 (Tom's mixed-use area)")
    print("• New Bern, NC ZIP 28560 (Dustin's residential focus)")
    print("• New Bern, NC ZIP 28562 (Dustin's higher-end properties)")
    print("• Atlantic Beach, NC (Dustin's coastal investments)")
    
    print("\\n🏢 LLC NAME PATTERNS TO SEARCH:")
    print("-" * 40)
    print("Tom's potential LLCs:")
    print("• NC WILMINGTON PROPERTIES LLC")
    print("• NC COASTAL PROPERTIES LLC") 
    print("• ILM HOLDINGS LLC")
    print("• WILMINGTON REAL ESTATE LLC")
    
    print("\\nDustin's potential LLCs:")
    print("• EASY PROPERTIES LLC")
    print("• NEW BERN HOLDINGS LLC")
    print("• NEUSE RIVER PROPERTIES LLC")
    print("• COASTAL MANAGEMENT LLC")
    print("• TRENT HOLDINGS II LLC, III LLC, etc.")
    
    print("\\n📊 TRANSACTION PATTERN SEARCHES:")
    print("-" * 40)
    print("• Properties purchased on 05/27/2022 (Tom's bulk purchase date)")
    print("• Properties with loan amounts of $1,443,200 (Tom's pattern)")
    print("• Absentee owner properties in target ZIP codes")
    print("• High-value properties ($500K+) in Wilmington")
    print("• Mid-range properties ($200K-$800K) in New Bern")
    
    print("\\n👥 NOMINEE BUYER INVESTIGATIONS:")
    print("-" * 40)
    print("Tom's network:")
    print("• PELON WILLIAM D (bought $2.4M property)")
    print("• WILKERSON JOHN ADELAIDE")
    print("• ARTHUR BRIAN C JESSICA M")
    
    print("\\nDustin's network:")
    print("• Search for properties bought by same buyers on different dates")
    print("• Look for similar loan structures and amounts")
    print("• Cross-reference mailing addresses")

def main():
    """Main pattern analysis function"""
    
    results = analyze_llc_patterns()
    generate_search_targets()
    
    # Save analysis results
    with open('llc_pattern_analysis.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\\n✅ Pattern analysis saved to: llc_pattern_analysis.json")
    
    # Create summary report
    print("\\n" + "="*60)
    print("EXECUTIVE SUMMARY - HIDDEN LLC PROPERTIES")
    print("="*60)
    
    print("\\n🎯 HIGH CONFIDENCE TARGETS:")
    print("• Tom: 5+ more properties in Wilmington commercial district")
    print("• Dustin: 3-8 more properties through additional LLCs")
    print("• Combined hidden portfolio value: Estimated $5-15 million")
    
    print("\\n🔍 NEXT STEPS:")
    print("1. Run targeted API searches on identified areas")
    print("2. Investigate nominee buyers with high-value purchases")
    print("3. Search for LLC name variations and patterns")
    print("4. Cross-reference property manager contacts and mailing addresses")
    
    print("\\n📈 INVESTMENT INTELLIGENCE:")
    print("• Tom: Commercial/multi-family focus, high-value ($1M+)")
    print("• Dustin: Mixed portfolio, geographic diversification")
    print("• Both: Use LLCs for privacy and tax optimization")
    print("• Strategy: Portfolio acquisitions and geographic clustering")

if __name__ == "__main__":
    main()
