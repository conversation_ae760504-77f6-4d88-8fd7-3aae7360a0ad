#!/bin/bash

# Section 8 Property Monitor - Cloud Deployment Script
# This script sets up and runs the Section 8 property monitoring system

set -e

echo "🏠 Section 8 Property Monitor - Cloud Setup"
echo "============================================"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

echo "✅ Docker and Docker Compose are installed"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p section8_reports
mkdir -p data
mkdir -p logs

# Set permissions
chmod 755 section8_reports
chmod 755 data
chmod 755 logs

echo "✅ Directories created"

# Build and start the container
echo "🔧 Building and starting Section 8 Monitor..."
docker-compose build
docker-compose up -d

echo "✅ Section 8 Property Monitor is now running!"

# Show container status
echo ""
echo "📊 Container Status:"
docker-compose ps

echo ""
echo "📋 Monitor Information:"
echo "  • Searches run every 1 minute"
echo "  • Reports saved to: ./section8_reports/"
echo "  • Database: ./section8_properties.db"
echo "  • Logs: ./section8_monitor.log"

echo ""
echo "🔍 Useful Commands:"
echo "  • View logs: docker-compose logs -f"
echo "  • Stop monitor: docker-compose down"
echo "  • Restart monitor: docker-compose restart"
echo "  • View database: sqlite3 section8_properties.db"

echo ""
echo "📧 Email Alerts:"
echo "  • High priority properties will trigger email alerts"
echo "  • Check your email for new opportunities"

echo ""
echo "🎯 What happens next:"
echo "  1. Monitor searches 10 NC markets every minute"
echo "  2. Analyzes properties for Section 8 viability"
echo "  3. Saves high-quality opportunities to daily reports"
echo "  4. Sends email alerts for high-priority properties"
echo "  5. Builds database of investment opportunities"

echo ""
echo "✅ Setup complete! Your Section 8 property monitor is now running in the cloud."
