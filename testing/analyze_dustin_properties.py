#!/usr/bin/env python3
"""
Real Estate API Property Analysis Script for Dustin's Section 8 Properties
This script analyzes <PERSON>'s property portfolio to identify common characteristics
"""

import requests
import json
import pandas as pd
from datetime import datetime
import time

# API Configuration
API_KEY = "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
BASE_URL = "https://api.realestateapi.com"

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {API_KEY}"
}

def load_properties():
    """Load the list of <PERSON>'s properties"""
    with open('dustin_properties.json', 'r') as f:
        return json.load(f)

def get_property_details(address):
    """Get detailed property information using Property Detail API"""
    url = f"{BASE_URL}/v2/PropertyDetail"
    
    payload = {
        "address": address
    }
    
    print(f"Fetching details for: {address}")
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Success: {address}")
            return data
        else:
            print(f"✗ Error for {address}: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Exception for {address}: {str(e)}")
        return None

def get_demographics_and_fair_market_rent(address):
    """Extract demographics and fair market rent data from property details"""
    details = get_property_details(address)
    
    if not details:
        return None
    
    # Extract key information for Section 8 analysis
    property_info = {}
    
    # Basic property info
    property_info['address'] = address
    property_info['bedrooms'] = details.get('propertyInfo', {}).get('bedrooms')
    property_info['bathrooms'] = details.get('propertyInfo', {}).get('bathrooms')
    property_info['square_feet'] = details.get('propertyInfo', {}).get('squareFeet')
    property_info['year_built'] = details.get('propertyInfo', {}).get('yearBuilt')
    property_info['property_type'] = details.get('propertyType')
    property_info['estimated_value'] = details.get('estimatedValue')
    
    # Demographics and HUD data
    demographics = details.get('demographics', {})
    property_info['median_household_income'] = demographics.get('medianHouseholdIncome')
    property_info['median_age'] = demographics.get('medianAge')
    property_info['population'] = demographics.get('population')
    property_info['average_house_value'] = demographics.get('averageHouseValue')
    
    # Fair Market Rent (crucial for Section 8)
    fair_market_rent = demographics.get('fairMarketRent', {})
    property_info['fmr_efficiency'] = fair_market_rent.get('efficiency')
    property_info['fmr_1br'] = fair_market_rent.get('oneBedroom')
    property_info['fmr_2br'] = fair_market_rent.get('twoBedroom')
    property_info['fmr_3br'] = fair_market_rent.get('threeBedroom')
    property_info['fmr_4br'] = fair_market_rent.get('fourBedroom')
    
    # HUD designations (important for Section 8)
    property_info['hud_designations'] = demographics.get('hudDesignations', [])
    
    # Neighborhood info
    neighborhood = details.get('neighborhood', {})
    property_info['neighborhood_name'] = neighborhood.get('neighborhoodName')
    property_info['neighborhood_type'] = neighborhood.get('neighborhoodType')
    
    # Owner info
    owner_info = details.get('ownerInfo', {})
    property_info['owner_occupied'] = details.get('ownerOccupied', False)
    property_info['absentee_owner'] = details.get('absenteeOwner', False)
    property_info['years_owned'] = owner_info.get('yearsOwned')
    property_info['estimated_equity'] = owner_info.get('estimatedEquity')
    
    return property_info

def analyze_section8_characteristics(properties_data):
    """Analyze the properties for common Section 8 characteristics"""
    
    print("\\n" + "="*60)
    print("SECTION 8 PROPERTY ANALYSIS")
    print("="*60)
    
    # Convert to DataFrame for easier analysis
    df = pd.DataFrame(properties_data)
    
    print(f"\\nTotal Properties Analyzed: {len(df)}")
    
    # Bedroom/Bathroom Distribution
    print("\\n📊 BEDROOM/BATHROOM DISTRIBUTION:")
    print("-" * 40)
    bedroom_counts = df['bedrooms'].value_counts().sort_index()
    for bedrooms, count in bedroom_counts.items():
        print(f"{bedrooms} bedrooms: {count} properties")
    
    # Property Types
    print("\\n🏠 PROPERTY TYPES:")
    print("-" * 40)
    type_counts = df['property_type'].value_counts()
    for prop_type, count in type_counts.items():
        print(f"{prop_type}: {count} properties")
    
    # Year Built Analysis
    print("\\n📅 YEAR BUILT ANALYSIS:")
    print("-" * 40)
    avg_year = df['year_built'].mean()
    min_year = df['year_built'].min()
    max_year = df['year_built'].max()
    print(f"Average Year Built: {avg_year:.0f}")
    print(f"Range: {min_year} - {max_year}")
    
    # Square Footage Analysis
    print("\\n📐 SQUARE FOOTAGE ANALYSIS:")
    print("-" * 40)
    avg_sqft = df['square_feet'].mean()
    min_sqft = df['square_feet'].min()
    max_sqft = df['square_feet'].max()
    print(f"Average Square Feet: {avg_sqft:.0f}")
    print(f"Range: {min_sqft} - {max_sqft} sq ft")
    
    # Fair Market Rent Analysis (Key for Section 8)
    print("\\n💰 FAIR MARKET RENT ANALYSIS:")
    print("-" * 40)
    for br in ['fmr_1br', 'fmr_2br', 'fmr_3br', 'fmr_4br']:
        if br in df.columns:
            avg_rent = df[br].mean()
            if not pd.isna(avg_rent):
                bedroom_num = br.split('_')[1].replace('br', ' bedroom')
                print(f"Average FMR {bedroom_num}: ${avg_rent:.0f}")
    
    # HUD Designations
    print("\\n🏛️ HUD DESIGNATIONS:")
    print("-" * 40)
    all_designations = []
    for designations in df['hud_designations']:
        if isinstance(designations, list):
            all_designations.extend(designations)
    
    if all_designations:
        unique_designations = set(all_designations)
        for designation in unique_designations:
            count = all_designations.count(designation)
            print(f"{designation}: {count} properties")
    else:
        print("No HUD designations found")
    
    # Geographic Clustering
    print("\\n🗺️ GEOGRAPHIC ANALYSIS:")
    print("-" * 40)
    zip_codes = df['address'].str.extract(r'(\\d{5})$')[0]
    zip_counts = zip_codes.value_counts()
    print("Properties by ZIP code:")
    for zip_code, count in zip_counts.items():
        print(f"  {zip_code}: {count} properties")
    
    # Investment Characteristics
    print("\\n💼 INVESTMENT CHARACTERISTICS:")
    print("-" * 40)
    absentee_count = df['absentee_owner'].sum()
    owner_occupied_count = df['owner_occupied'].sum()
    print(f"Absentee Owner Properties: {absentee_count}")
    print(f"Owner Occupied Properties: {owner_occupied_count}")
    
    avg_value = df['estimated_value'].mean()
    print(f"Average Estimated Value: ${avg_value:,.0f}")
    
    return df

def main():
    """Main analysis function"""
    print("Starting Section 8 Property Analysis for Dustin's Portfolio...")
    print("="*60)
    
    # Load properties
    properties = load_properties()
    print(f"Loaded {len(properties)} properties to analyze")
    
    # Analyze each property
    properties_data = []
    failed_properties = []
    
    for i, address in enumerate(properties):
        print(f"\\nProgress: {i+1}/{len(properties)}")
        
        property_data = get_demographics_and_fair_market_rent(address)
        
        if property_data:
            properties_data.append(property_data)
        else:
            failed_properties.append(address)
        
        # Be respectful to the API
        time.sleep(1)
    
    print(f"\\nCompleted analysis:")
    print(f"✓ Successful: {len(properties_data)} properties")
    print(f"✗ Failed: {len(failed_properties)} properties")
    
    if failed_properties:
        print("\\nFailed properties:")
        for addr in failed_properties:
            print(f"  - {addr}")
    
    # Save raw data
    with open('property_analysis_results.json', 'w') as f:
        json.dump(properties_data, f, indent=2)
    
    print("\\nRaw data saved to: property_analysis_results.json")
    
    # Perform analysis
    if properties_data:
        df = analyze_section8_characteristics(properties_data)
        
        # Save analysis to CSV
        df.to_csv('dustin_properties_analysis.csv', index=False)
        print("\\nDetailed analysis saved to: dustin_properties_analysis.csv")
        
        # Generate Section 8 insights
        print("\\n" + "="*60)
        print("SECTION 8 INSIGHTS & RECOMMENDATIONS")
        print("="*60)
        
        # Key characteristics for Section 8 properties
        common_bedrooms = df['bedrooms'].mode().iloc[0] if not df['bedrooms'].mode().empty else "N/A"
        avg_rent_for_common_br = None
        
        if common_bedrooms == 2:
            avg_rent_for_common_br = df['fmr_2br'].mean()
        elif common_bedrooms == 3:
            avg_rent_for_common_br = df['fmr_3br'].mean()
        elif common_bedrooms == 1:
            avg_rent_for_common_br = df['fmr_1br'].mean()
        
        print(f"\\n🎯 COMMON CHARACTERISTICS:")
        print(f"Most common bedroom count: {common_bedrooms}")
        if avg_rent_for_common_br and not pd.isna(avg_rent_for_common_br):
            print(f"Average FMR for {common_bedrooms}BR: ${avg_rent_for_common_br:.0f}")
        
        print(f"Average property age: {2024 - df['year_built'].mean():.0f} years")
        print(f"Average square footage: {df['square_feet'].mean():.0f} sq ft")
        
        # Geographic concentration
        most_common_zip = zip_codes.mode().iloc[0] if not zip_codes.mode().empty else "N/A"
        zip_concentration = zip_codes.value_counts().iloc[0] / len(zip_codes) * 100
        print(f"Geographic concentration: {zip_concentration:.0f}% in ZIP {most_common_zip}")
        
    else:
        print("\\nNo property data was successfully retrieved for analysis.")

if __name__ == "__main__":
    main()
