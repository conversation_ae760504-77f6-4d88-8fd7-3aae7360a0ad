#!/usr/bin/env python3
"""
LLC Analysis Results Visualizer
Create simple visualizations of LLC property discovery results
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os

def load_results():
    """Load analysis results from JSON files"""
    results = {}
    
    files_to_load = [
        "llc_pattern_analysis.json",
        "targeted_llc_search_results.json",
        "llc_discovery_results.json"
    ]
    
    for filename in files_to_load:
        if os.path.exists(filename):
            try:
                with open(filename, 'r') as f:
                    results[filename.replace('.json', '')] = json.load(f)
                print(f"✅ Loaded {filename}")
            except Exception as e:
                print(f"❌ Error loading {filename}: {str(e)}")
        else:
            print(f"⚠️ {filename} not found")
    
    return results

def create_portfolio_summary():
    """Create visual summary of known vs potential properties"""
    
    # Known LLC properties
    known_properties = {
        "Tom's NC ILM Properties": {
            "count": 5,
            "value": 8875000,
            "type": "Commercial"
        },
        "Dustin's SO EASY LLC": {
            "count": 1,
            "value": 1750000,
            "type": "High-End Residential"
        },
        "Dustin's GAGE Properties": {
            "count": 1,
            "value": 675000,
            "type": "Commercial"
        },
        "Dustin's CAR Management": {
            "count": 1,
            "value": 600000,
            "type": "Commercial"
        },
        "Dustin's Other LLCs": {
            "count": 3,
            "value": 500000,  # Estimated
            "type": "Mixed"
        }
    }
    
    # Create visualization
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Property count by LLC
    llc_names = list(known_properties.keys())
    counts = [known_properties[llc]["count"] for llc in llc_names]
    
    ax1.bar(range(len(llc_names)), counts, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
    ax1.set_xticks(range(len(llc_names)))
    ax1.set_xticklabels([name.replace("'s ", "\\n") for name in llc_names], rotation=45, ha='right')
    ax1.set_ylabel('Number of Properties')
    ax1.set_title('Known LLC Properties by Count')
    ax1.grid(axis='y', alpha=0.3)
    
    # Portfolio value by LLC
    values = [known_properties[llc]["value"] for llc in llc_names]
    
    ax2.bar(range(len(llc_names)), [v/1000000 for v in values], color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
    ax2.set_xticks(range(len(llc_names)))
    ax2.set_xticklabels([name.replace("'s ", "\\n") for name in llc_names], rotation=45, ha='right')
    ax2.set_ylabel('Portfolio Value ($ Millions)')
    ax2.set_title('Known LLC Portfolio Values')
    ax2.grid(axis='y', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('llc_portfolio_summary.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return sum(values)

def create_geographic_analysis():
    """Analyze geographic distribution of properties"""
    
    # Geographic data
    geographic_data = {
        'Wilmington, NC': {'Tom': 5, 'Dustin': 0, 'Value_Tom': 8875000, 'Value_Dustin': 0},
        'New Bern, NC': {'Tom': 0, 'Dustin': 4, 'Value_Tom': 0, 'Value_Dustin': 3025000},
        'Atlantic Beach, NC': {'Tom': 0, 'Dustin': 1, 'Value_Tom': 0, 'Value_Dustin': 300000},
        'Miami, FL': {'Tom': 1, 'Dustin': 0, 'Value_Tom': 17750000, 'Value_Dustin': 0}
    }
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Property count by location
    locations = list(geographic_data.keys())
    tom_counts = [geographic_data[loc]['Tom'] for loc in locations]
    dustin_counts = [geographic_data[loc]['Dustin'] for loc in locations]
    
    x = range(len(locations))
    width = 0.35
    
    ax1.bar([i - width/2 for i in x], tom_counts, width, label='Tom', color='#1f77b4')
    ax1.bar([i + width/2 for i in x], dustin_counts, width, label='Dustin', color='#ff7f0e')
    
    ax1.set_xlabel('Location')
    ax1.set_ylabel('Number of Properties')
    ax1.set_title('Property Count by Geographic Location')
    ax1.set_xticks(x)
    ax1.set_xticklabels(locations, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(axis='y', alpha=0.3)
    
    # Portfolio value by location
    tom_values = [geographic_data[loc]['Value_Tom']/1000000 for loc in locations]
    dustin_values = [geographic_data[loc]['Value_Dustin']/1000000 for loc in locations]
    
    ax2.bar([i - width/2 for i in x], tom_values, width, label='Tom', color='#1f77b4')
    ax2.bar([i + width/2 for i in x], dustin_values, width, label='Dustin', color='#ff7f0e')
    
    ax2.set_xlabel('Location')
    ax2.set_ylabel('Portfolio Value ($ Millions)')
    ax2.set_title('Portfolio Value by Geographic Location')
    ax2.set_xticks(x)
    ax2.set_xticklabels(locations, rotation=45, ha='right')
    ax2.legend()
    ax2.grid(axis='y', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('geographic_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_discovery_potential():
    """Visualize discovery potential and confidence levels"""
    
    # Estimated discovery potential
    discovery_data = {
        'Known Properties': {'Tom': 5, 'Dustin': 6},
        'High Confidence': {'Tom': 3, 'Dustin': 5},
        'Medium Confidence': {'Tom': 2, 'Dustin': 8},
        'Total Potential': {'Tom': 10, 'Dustin': 19}
    }
    
    categories = list(discovery_data.keys())
    tom_data = [discovery_data[cat]['Tom'] for cat in categories]
    dustin_data = [discovery_data[cat]['Dustin'] for cat in categories]
    
    fig, ax = plt.subplots(figsize=(12, 6))
    
    x = range(len(categories))
    width = 0.35
    
    bars1 = ax.bar([i - width/2 for i in x], tom_data, width, label='Tom', color='#1f77b4')
    bars2 = ax.bar([i + width/2 for i in x], dustin_data, width, label='Dustin', color='#ff7f0e')
    
    ax.set_xlabel('Property Categories')
    ax.set_ylabel('Number of Properties')
    ax.set_title('LLC Property Discovery Potential')
    ax.set_xticks(x)
    ax.set_xticklabels(categories)
    ax.legend()
    ax.grid(axis='y', alpha=0.3)
    
    # Add value labels on bars
    for bar in bars1:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{int(height)}', ha='center', va='bottom')
    
    for bar in bars2:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{int(height)}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('discovery_potential.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_summary_report():
    """Generate text summary report"""
    
    print("\\n" + "="*60)
    print("LLC PROPERTY DISCOVERY - VISUAL ANALYSIS SUMMARY")
    print("="*60)
    
    print(f"\\nGenerated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\\n📊 CONFIRMED LLC HOLDINGS:")
    print("-" * 40)
    print("Tom's Portfolio:")
    print("  • NC ILM PROPERTIES LLC: 5 properties ($8.9M)")
    print("  • Personal Holdings: 1 property ($17.8M Miami)")
    print("  • Total Confirmed Value: $26.7M")
    
    print("\\nDustin's Portfolio:")
    print("  • 6+ Different LLCs: ~6-10 properties")
    print("  • Geographic Focus: New Bern, NC + Coastal")
    print("  • Estimated Value: $3-5M confirmed")
    
    print("\\n🎯 DISCOVERY POTENTIAL:")
    print("-" * 40)
    print("High Confidence Additional Properties:")
    print("  • Tom: 3-5 more Wilmington properties ($3-8M)")
    print("  • Dustin: 5-8 more New Bern properties ($2-5M)")
    
    print("\\nTotal Hidden Portfolio Estimate: $5-13M")
    
    print("\\n📍 GEOGRAPHIC CONCENTRATIONS:")
    print("-" * 40)
    print("• Wilmington, NC - Tom's commercial focus")
    print("• New Bern, NC - Dustin's residential focus")
    print("• Atlantic Beach, NC - Dustin's coastal properties")
    print("• Miami, FL - Tom's luxury holdings")
    
    print("\\n💡 KEY INSIGHTS:")
    print("-" * 40)
    print("• Tom: Portfolio acquisition strategy (bulk purchases)")
    print("• Dustin: Diversified LLC strategy (privacy/taxes)")
    print("• Both: Geographic clustering for management efficiency")
    print("• Combined: $30-40M total estimated portfolio")
    
    print("\\n📈 NEXT INVESTIGATION TARGETS:")
    print("-" * 40)
    print("1. More Wilmington commercial properties (Tom)")
    print("2. Additional New Bern residential properties (Dustin)")
    print("3. Family member/nominee buyer properties")
    print("4. LLC name variations and new entities")
    
    return True

def main():
    """Main visualization function"""
    
    print("LLC PROPERTY DISCOVERY - RESULTS VISUALIZATION")
    print("="*60)
    
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
    except ImportError:
        print("📦 Installing visualization packages...")
        os.system("pip install matplotlib seaborn")
        import matplotlib.pyplot as plt
        import seaborn as sns
    
    # Set style
    plt.style.use('default')
    sns.set_palette("husl")
    
    print("\\n📊 Creating visualizations...")
    
    # Load results
    results = load_results()
    
    # Create visualizations
    print("\\n1. Portfolio Summary...")
    total_value = create_portfolio_summary()
    
    print("\\n2. Geographic Analysis...")
    create_geographic_analysis()
    
    print("\\n3. Discovery Potential...")
    create_discovery_potential()
    
    print("\\n4. Summary Report...")
    generate_summary_report()
    
    print("\\n✅ All visualizations created!")
    print("\\n📁 Generated Files:")
    print("  • llc_portfolio_summary.png")
    print("  • geographic_analysis.png") 
    print("  • discovery_potential.png")
    
    print(f"\\n💰 Total Known Portfolio Value: ${total_value:,.0f}")
    print("🎯 Estimated Hidden Portfolio: $5-15 Million")

if __name__ == "__main__":
    main()
