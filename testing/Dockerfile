# Use official Python runtime as base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements_cloud.txt .
RUN pip install --no-cache-dir -r requirements_cloud.txt

# Copy application code
COPY section8_cloud_monitor.py .
COPY config.json .

# Create directories for data persistence
RUN mkdir -p /app/section8_reports /app/data

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Run the monitor
CMD ["python", "section8_cloud_monitor.py"]
