version: '3.8'

services:
  section8-monitor:
    build: .
    container_name: section8_property_monitor
    restart: unless-stopped
    volumes:
      - ./section8_reports:/app/section8_reports
      - ./data:/app/data
      - ./section8_monitor.log:/app/section8_monitor.log
      - ./section8_properties.db:/app/section8_properties.db
    environment:
      - PYTHONUNBUFFERED=1
      - TZ=America/New_York
    healthcheck:
      test: ["CMD", "python", "-c", "import sqlite3; conn = sqlite3.connect('/app/section8_properties.db'); conn.close()"]
      interval: 5m
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  dashboard:
    build: .
    container_name: section8_dashboard
    restart: unless-stopped
    command: python dashboard.py
    ports:
      - "8080:8080"
    volumes:
      - ./section8_properties.db:/app/section8_properties.db
      - ./section8_monitor.log:/app/section8_monitor.log
    environment:
      - PYTHONUNBUFFERED=1
    depends_on:
      - section8-monitor
