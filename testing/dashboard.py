#!/usr/bin/env python3
"""
Section 8 Monitor - Simple Web Dashboard
View current status and recent findings via web interface
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import sqlite3
import os
from datetime import datetime, timedelta
import urllib.parse

class DashboardHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_dashboard()
        elif self.path == '/api/stats':
            self.send_stats()
        elif self.path == '/api/recent':
            self.send_recent_properties()
        else:
            self.send_404()
    
    def send_dashboard(self):
        """Send main dashboard HTML"""
        html = """
<!DOCTYPE html>
<html>
<head>
    <title>Section 8 Property Monitor Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2em; font-weight: bold; color: #2c3e50; }
        .stat-label { color: #7f8c8d; margin-top: 5px; }
        .properties { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .property { border-bottom: 1px solid #ecf0f1; padding: 15px 0; }
        .property:last-child { border-bottom: none; }
        .priority-high { border-left: 4px solid #e74c3c; padding-left: 15px; }
        .priority-medium { border-left: 4px solid #f39c12; padding-left: 15px; }
        .priority-low { border-left: 4px solid #95a5a6; padding-left: 15px; }
        .property-address { font-weight: bold; font-size: 1.1em; color: #2c3e50; }
        .property-price { color: #27ae60; font-weight: bold; }
        .property-details { color: #7f8c8d; margin-top: 5px; }
        .refresh-btn { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        .status { background: #d5e8d4; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 Section 8 Property Monitor</h1>
            <p>Continuous monitoring for Section 8 investment opportunities</p>
            <p><small>Last updated: <span id="last-update">Loading...</span></small></p>
        </div>
        
        <div class="status" id="status">
            <strong>Status:</strong> <span id="monitor-status">Checking...</span>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-properties">-</div>
                <div class="stat-label">Total Properties Found</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="high-priority">-</div>
                <div class="stat-label">High Priority Opportunities</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avg-roi">-</div>
                <div class="stat-label">Average ROI %</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="today-count">-</div>
                <div class="stat-label">Found Today</div>
            </div>
        </div>
        
        <div class="properties">
            <h2>Recent High Priority Properties</h2>
            <button class="refresh-btn" onclick="loadData()">Refresh Data</button>
            <div id="properties-list">
                <p>Loading properties...</p>
            </div>
        </div>
    </div>
    
    <script>
        function loadData() {
            // Load stats
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('total-properties').textContent = data.total_properties || '0';
                    document.getElementById('high-priority').textContent = data.high_priority || '0';
                    document.getElementById('avg-roi').textContent = (data.avg_roi || 0).toFixed(1) + '%';
                    document.getElementById('today-count').textContent = data.today_count || '0';
                    document.getElementById('monitor-status').textContent = data.status || 'Unknown';
                    document.getElementById('last-update').textContent = new Date().toLocaleString();
                })
                .catch(error => {
                    console.error('Error loading stats:', error);
                    document.getElementById('monitor-status').textContent = 'Error loading data';
                });
            
            // Load recent properties
            fetch('/api/recent')
                .then(response => response.json())
                .then(data => {
                    const propertiesList = document.getElementById('properties-list');
                    if (data.properties && data.properties.length > 0) {
                        propertiesList.innerHTML = data.properties.map(prop => `
                            <div class="property priority-${prop.priority.toLowerCase()}">
                                <div class="property-address">${prop.address}</div>
                                <div class="property-price">$${prop.price.toLocaleString()}</div>
                                <div class="property-details">
                                    ${prop.bedrooms}BR/${prop.bathrooms}BA | ${prop.square_feet} sq ft | 
                                    ROI: ${prop.roi_percentage.toFixed(1)}% | 
                                    Priority: ${prop.priority} | 
                                    Found: ${new Date(prop.first_seen).toLocaleDateString()}
                                </div>
                            </div>
                        `).join('');
                    } else {
                        propertiesList.innerHTML = '<p>No properties found yet. Monitor is searching...</p>';
                    }
                })
                .catch(error => {
                    console.error('Error loading properties:', error);
                    document.getElementById('properties-list').innerHTML = '<p>Error loading properties data.</p>';
                });
        }
        
        // Load data on page load
        loadData();
        
        // Auto-refresh every 60 seconds
        setInterval(loadData, 60000);
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def send_stats(self):
        """Send dashboard statistics"""
        try:
            conn = sqlite3.connect('section8_properties.db')
            cursor = conn.cursor()
            
            # Get total properties
            cursor.execute("SELECT COUNT(*) FROM monitored_properties")
            total_properties = cursor.fetchone()[0]
            
            # Get high priority count
            cursor.execute("SELECT COUNT(*) FROM monitored_properties WHERE priority='HIGH'")
            high_priority = cursor.fetchone()[0]
            
            # Get average ROI
            cursor.execute("SELECT AVG(roi_percentage) FROM monitored_properties WHERE roi_percentage > 0")
            avg_roi = cursor.fetchone()[0] or 0
            
            # Get today's count
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute("SELECT COUNT(*) FROM monitored_properties WHERE DATE(first_seen) = ?", (today,))
            today_count = cursor.fetchone()[0]
            
            conn.close()
            
            stats = {
                'total_properties': total_properties,
                'high_priority': high_priority,
                'avg_roi': avg_roi,
                'today_count': today_count,
                'status': 'Running' if os.path.exists('section8_monitor.log') else 'Unknown'
            }
            
        except Exception as e:
            stats = {
                'total_properties': 0,
                'high_priority': 0,
                'avg_roi': 0,
                'today_count': 0,
                'status': f'Error: {str(e)}'
            }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(stats).encode())
    
    def send_recent_properties(self):
        """Send recent high priority properties"""
        try:
            conn = sqlite3.connect('section8_properties.db')
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT address, price, bedrooms, bathrooms, square_feet, 
                       roi_percentage, priority, first_seen
                FROM monitored_properties 
                WHERE priority IN ('HIGH', 'MEDIUM')
                ORDER BY first_seen DESC 
                LIMIT 20
            """)
            
            rows = cursor.fetchall()
            properties = []
            
            for row in rows:
                properties.append({
                    'address': row[0],
                    'price': row[1],
                    'bedrooms': row[2],
                    'bathrooms': row[3],
                    'square_feet': row[4],
                    'roi_percentage': row[5],
                    'priority': row[6],
                    'first_seen': row[7]
                })
            
            conn.close()
            
        except Exception as e:
            properties = []
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'properties': properties}).encode())
    
    def send_404(self):
        """Send 404 response"""
        self.send_response(404)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(b'<h1>404 Not Found</h1>')

def run_dashboard(port=8080):
    """Run the dashboard server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, DashboardHandler)
    print(f"Dashboard running at http://localhost:{port}")
    print("Press Ctrl+C to stop")
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\\nDashboard stopped")
        httpd.shutdown()

if __name__ == "__main__":
    run_dashboard()
