#!/usr/bin/env python3
"""
Test the corrected Real Estate API implementation
"""

import asyncio
import aiohttp
import os
import json
from dotenv import load_dotenv

async def test_corrected_api():
    """Test the corrected Real Estate API with proper POST format"""
    print("🔧 Testing Corrected Real Estate API Implementation")
    print("=" * 60)
    
    load_dotenv()
    
    api_key = os.getenv('REAL_ESTATE_API_KEY')
    base_url = os.getenv('REAL_ESTATE_API_URL')
    
    print(f"🌐 API URL: {base_url}/v2/PropertySearch")
    print(f"🔑 API Key: {api_key[:10]}...{api_key[-10:]}")
    print()
    
    # Correct payload format based on documentation
    test_payloads = [
        # Test 1: Basic city search
        {
            "name": "Basic Detroit Search",
            "payload": {
                "city": "Detroit",
                "state": "MI",
                "beds_min": 2,
                "beds_max": 5,
                "value_min": 30000,
                "value_max": 400000,
                "property_type": "SFR,MFR",
                "limit": 5
            }
        },
        
        # Test 2: Cleveland search
        {
            "name": "Basic Cleveland Search", 
            "payload": {
                "city": "Cleveland",
                "state": "OH",
                "beds_min": 2,
                "beds_max": 4,
                "value_min": 25000,
                "value_max": 300000,
                "property_type": "SFR",
                "limit": 5
            }
        },
        
        # Test 3: Count only search
        {
            "name": "Count Only Search",
            "payload": {
                "city": "Detroit",
                "state": "MI",
                "beds_min": 2,
                "value_min": 30000,
                "value_max": 400000,
                "count": True
            }
        }
    ]
    
    # Test different header formats
    header_formats = [
        {
            "name": "Bearer Token",
            "headers": {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
        },
        {
            "name": "X-API-Key",
            "headers": {
                'X-API-Key': api_key,
                'Content-Type': 'application/json'
            }
        },
        {
            "name": "Token Auth",
            "headers": {
                'Authorization': f'Token {api_key}',
                'Content-Type': 'application/json'
            }
        }
    ]
    
    url = f"{base_url}/v2/PropertySearch"
    
    async with aiohttp.ClientSession() as session:
        for test_case in test_payloads:
            print(f"🧪 Testing: {test_case['name']}")
            print(f"   Payload: {json.dumps(test_case['payload'], indent=2)}")
            
            for header_format in header_formats:
                try:
                    print(f"   🔄 Trying {header_format['name']} authentication...")
                    
                    async with session.post(
                        url, 
                        json=test_case['payload'], 
                        headers=header_format['headers'],
                        timeout=15
                    ) as response:
                        
                        print(f"      Status: {response.status}")
                        
                        if response.status == 200:
                            try:
                                data = await response.json()
                                print(f"      ✅ SUCCESS!")
                                
                                if isinstance(data, dict):
                                    print(f"      📊 Response keys: {list(data.keys())}")
                                    
                                    if 'properties' in data:
                                        properties = data['properties']
                                        print(f"      🏠 Properties found: {len(properties)}")
                                        
                                        if properties:
                                            sample_prop = properties[0]
                                            print(f"      📍 Sample property keys: {list(sample_prop.keys())}")
                                            
                                            # Show key property details
                                            if 'propertyInfo' in sample_prop:
                                                prop_info = sample_prop['propertyInfo']
                                                address = prop_info.get('address', {})
                                                print(f"      🏡 Sample: {address.get('address', 'N/A')}, {address.get('city', 'N/A')}")
                                                print(f"      💰 Value: ${prop_info.get('estimatedValue', 'N/A'):,}")
                                                print(f"      🛏️ Beds/Baths: {prop_info.get('bedrooms', 'N/A')}/{prop_info.get('bathrooms', 'N/A')}")
                                    
                                    elif 'count' in data:
                                        print(f"      📊 Total count: {data['count']}")
                                    
                                    elif 'results' in data:
                                        results = data['results']
                                        print(f"      🏠 Results found: {len(results)}")
                                
                                elif isinstance(data, list):
                                    print(f"      🏠 Direct list with {len(data)} items")
                                
                                print(f"      🎉 This combination works! Use this format.")
                                return True
                                
                            except json.JSONDecodeError:
                                text = await response.text()
                                print(f"      📄 Non-JSON response: {text[:100]}...")
                                
                        elif response.status == 401:
                            print(f"      🔐 Authentication failed")
                        elif response.status == 403:
                            print(f"      🚫 Forbidden - check permissions")
                        elif response.status == 404:
                            print(f"      ❌ Endpoint not found")
                        elif response.status == 422:
                            print(f"      ⚠️ Validation error - check payload format")
                            try:
                                error_data = await response.json()
                                print(f"      📋 Error details: {error_data}")
                            except:
                                error_text = await response.text()
                                print(f"      📋 Error text: {error_text[:200]}...")
                        elif response.status == 429:
                            print(f"      ⏰ Rate limited")
                        else:
                            print(f"      ⚠️ Unexpected status")
                            try:
                                error_text = await response.text()
                                print(f"      📋 Response: {error_text[:200]}...")
                            except:
                                pass
                                
                except asyncio.TimeoutError:
                    print(f"      ⏱️ Request timed out")
                except Exception as e:
                    print(f"      💥 Error: {str(e)[:100]}...")
                
                print()
            
            print("-" * 40)
    
    print("❌ No working combination found")
    print()
    print("🔧 NEXT STEPS:")
    print("1. Verify API key is active and has proper permissions")
    print("2. Check if account has sufficient credits")
    print("3. Contact RealEstateAPI support for assistance")
    print("4. Consider using alternative data sources")
    
    return False

if __name__ == "__main__":
    asyncio.run(test_corrected_api())
